function [R,tsmvalue,net,info] = objectiveFunction(x)
rng('default')                                                 %% 使训练集、和测试集的随机划分与主函数一致

%% 导入数据
% 带权重参数的DarkNet19
load DarkNet19.mat

%% 导入数据

% 震动数据
imds = imageDatastore('数据集224×224', ...
    'IncludeSubfolders', true, 'LabelSource', 'foldernames');

%% 指定训练集和测试集的比例
trainRatio = 0.8;            % 训练集占总数据的比例
testRatio = 1 - trainRatio;  % 测试集占总数据的比例

% 划分训练集和测试集
[train, test] = splitEachLabel(imds, trainRatio, 'random');

augmentrain = imageDataAugmenter('RandXReflection', true);
trainImds = augmentedImageDatastore([256 256], train,'ColorPreprocessing', 'gray2rgb', 'DataAugmentation', augmentrain);

augmentest = imageDataAugmenter('RandXReflection', true);
testImds = augmentedImageDatastore([256 256], test,'ColorPreprocessing', 'gray2rgb', 'DataAugmentation', augmentest);

numtypes = numel(unique(train.Labels));
%% 将优化目标参数传进来的值 转换为需要的超参数
learning_rate = x(1);                        %% 学习率
NumNeurons =round(x(2));                     %% 神经元个数

%% 利用寻优得到参数重新训练与预测 
%% 调用DarkNet
net = DarkNet19;
layersTransfer = net.Layers(1:end-3);

%% DarkNet后3层接上GRU-Attention组成DarkNet-GRU-Attention
layers = [
    layersTransfer
    flattenLayer("Name","flatten")
    gruLayer(NumNeurons,"Name","gru")
    selfAttentionLayer(4,256,"Name","selfattention")
    fullyConnectedLayer(numtypes )
    softmaxLayer
    classificationLayer];

%% 设置训练参数
options = trainingOptions('adam', ...        % Adam 梯度下降算法
    'MiniBatchSize',32, ...                  % 训练批次大小
    'InitialLearnRate',learning_rate, ...    % 初始学习率
    'MaxEpochs',1, ...                       % 最大训练次数
    'Shuffle','every-epoch', ...             % 每次训练打乱数据集
    'Verbose',true);                         % 有关训练进度的信息打印到命令窗口中

%% 网络训练
[net info] = trainNetwork(trainImds,layers,options);

%% 测试集预测
YPred = classify(net,testImds);
Ytest = test.Labels;

% 精度计算
accuracy = (sum(YPred == Ytest)/numel(Ytest))*100;

%% 结果导出
tsmvalue = YPred;
R=1-(sum(YPred == Ytest)/numel(Ytest));
disp(['本轮精度:', num2str(accuracy),'%']);

end

