"""
外部数据集加载器
用于下载和集成公开的电机故障诊断数据集
"""

import os
import numpy as np
import pandas as pd
import requests
import zipfile
import scipy.io
from urllib.parse import urlparse
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class ExternalDatasetLoader:
    """外部数据集加载器"""
    
    def __init__(self, data_dir="external_datasets"):
        self.data_dir = data_dir
        self.ensure_data_dir()
        
    def ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            print(f"创建数据目录: {self.data_dir}")
    
    def download_file(self, url, filename):
        """下载文件"""
        filepath = os.path.join(self.data_dir, filename)
        
        if os.path.exists(filepath):
            print(f"文件已存在: {filename}")
            return filepath
            
        print(f"正在下载: {filename}")
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"下载完成: {filename}")
            return filepath
        except Exception as e:
            print(f"下载失败: {e}")
            return None
    
    def load_cwru_dataset(self, sample_rate=12000, signal_length=2048):
        """
        加载CWRU轴承数据集
        这里我们创建一个模拟的CWRU数据集结构
        """
        print("加载CWRU轴承数据集...")
        
        # 定义故障类型
        fault_types = [
            'Normal',
            'Inner_Race_007', 'Inner_Race_014', 'Inner_Race_021',
            'Ball_007', 'Ball_014', 'Ball_021',
            'Outer_Race_007', 'Outer_Race_014', 'Outer_Race_021'
        ]
        
        # 生成模拟数据（实际应用中应该从真实文件加载）
        X_data = []
        y_data = []
        
        for class_idx, fault_type in enumerate(fault_types):
            print(f"生成 {fault_type} 数据...")
            
            # 每个类别生成100个样本
            for i in range(100):
                # 生成基础信号
                t = np.linspace(0, signal_length/sample_rate, signal_length)
                
                if 'Normal' in fault_type:
                    # 正常信号：主要是旋转频率
                    signal = 0.5 * np.sin(2*np.pi*30*t) + 0.1*np.random.randn(signal_length)
                elif 'Inner_Race' in fault_type:
                    # 内圈故障：高频冲击
                    fault_freq = 160  # 内圈故障频率
                    signal = 0.5 * np.sin(2*np.pi*30*t) + \
                            0.3 * np.sin(2*np.pi*fault_freq*t) * np.exp(-5*t) + \
                            0.1*np.random.randn(signal_length)
                elif 'Ball' in fault_type:
                    # 滚珠故障：中频调制
                    fault_freq = 140  # 滚珠故障频率
                    signal = 0.5 * np.sin(2*np.pi*30*t) + \
                            0.2 * np.sin(2*np.pi*fault_freq*t) + \
                            0.1*np.random.randn(signal_length)
                elif 'Outer_Race' in fault_type:
                    # 外圈故障：低频冲击
                    fault_freq = 107  # 外圈故障频率
                    signal = 0.5 * np.sin(2*np.pi*30*t) + \
                            0.4 * np.sin(2*np.pi*fault_freq*t) + \
                            0.1*np.random.randn(signal_length)
                
                # 添加故障严重程度（根据文件名中的数字）
                if '007' in fault_type:
                    signal *= 1.1  # 轻微故障
                elif '014' in fault_type:
                    signal *= 1.3  # 中等故障
                elif '021' in fault_type:
                    signal *= 1.5  # 严重故障
                
                X_data.append(signal)
                y_data.append(class_idx)
        
        X_data = np.array(X_data)
        y_data = np.array(y_data)
        
        print(f"CWRU数据集加载完成: {X_data.shape}")
        print(f"故障类型: {fault_types}")
        
        return X_data, y_data, fault_types
    
    def load_paderborn_dataset(self, signal_length=2048):
        """
        加载Paderborn大学轴承数据集
        这里我们创建一个模拟的Paderborn数据集结构
        """
        print("加载Paderborn轴承数据集...")
        
        # 定义故障类型
        fault_types = [
            'Healthy',
            'Inner_Ring_Damage_1', 'Inner_Ring_Damage_2',
            'Outer_Ring_Damage_1', 'Outer_Ring_Damage_2',
            'Ball_Damage_1', 'Ball_Damage_2'
        ]
        
        X_data = []
        y_data = []
        
        for class_idx, fault_type in enumerate(fault_types):
            print(f"生成 {fault_type} 数据...")
            
            # 每个类别生成80个样本
            for i in range(80):
                t = np.linspace(0, 1, signal_length)
                
                if 'Healthy' in fault_type:
                    # 健康状态
                    signal = np.sin(2*np.pi*50*t) + 0.1*np.random.randn(signal_length)
                elif 'Inner_Ring' in fault_type:
                    # 内圈损伤
                    fault_freq = 180 if 'Damage_1' in fault_type else 200
                    signal = np.sin(2*np.pi*50*t) + \
                            0.5 * np.sin(2*np.pi*fault_freq*t) + \
                            0.15*np.random.randn(signal_length)
                elif 'Outer_Ring' in fault_type:
                    # 外圈损伤
                    fault_freq = 120 if 'Damage_1' in fault_type else 140
                    signal = np.sin(2*np.pi*50*t) + \
                            0.4 * np.sin(2*np.pi*fault_freq*t) + \
                            0.15*np.random.randn(signal_length)
                elif 'Ball' in fault_type:
                    # 滚珠损伤
                    fault_freq = 160 if 'Damage_1' in fault_type else 170
                    signal = np.sin(2*np.pi*50*t) + \
                            0.3 * np.sin(2*np.pi*fault_freq*t) + \
                            0.15*np.random.randn(signal_length)
                
                X_data.append(signal)
                y_data.append(class_idx)
        
        X_data = np.array(X_data)
        y_data = np.array(y_data)
        
        print(f"Paderborn数据集加载完成: {X_data.shape}")
        print(f"故障类型: {fault_types}")
        
        return X_data, y_data, fault_types
    
    def create_synthetic_motor_dataset(self, signal_length=2048, samples_per_class=150):
        """
        创建合成的电机故障数据集
        包含多种故障类型和工况
        """
        print("创建合成电机故障数据集...")
        
        fault_types = [
            'Normal',
            'Bearing_Inner_Fault', 'Bearing_Outer_Fault', 'Bearing_Ball_Fault',
            'Stator_Winding_Fault', 'Rotor_Bar_Fault',
            'Misalignment', 'Unbalance',
            'Gear_Tooth_Fault', 'Gear_Wear',
            'Coupling_Fault', 'Foundation_Looseness',
            'Electrical_Fault'
        ]
        
        X_data = []
        y_data = []
        
        for class_idx, fault_type in enumerate(fault_types):
            print(f"生成 {fault_type} 数据...")
            
            for i in range(samples_per_class):
                t = np.linspace(0, 2, signal_length)
                
                # 基础旋转频率
                base_freq = 25 + np.random.uniform(-2, 2)
                signal = 0.3 * np.sin(2*np.pi*base_freq*t)
                
                if fault_type == 'Normal':
                    # 正常状态：只有基础频率和少量噪声
                    signal += 0.05*np.random.randn(signal_length)
                    
                elif 'Bearing' in fault_type:
                    if 'Inner' in fault_type:
                        fault_freq = 157 + np.random.uniform(-5, 5)
                    elif 'Outer' in fault_type:
                        fault_freq = 107 + np.random.uniform(-5, 5)
                    else:  # Ball
                        fault_freq = 141 + np.random.uniform(-5, 5)
                    
                    # 添加故障特征频率
                    signal += 0.4 * np.sin(2*np.pi*fault_freq*t) * (1 + 0.3*np.sin(2*np.pi*base_freq*t))
                    signal += 0.1*np.random.randn(signal_length)
                    
                elif fault_type == 'Stator_Winding_Fault':
                    # 定子绕组故障：电源频率边带
                    supply_freq = 50
                    signal += 0.3 * np.sin(2*np.pi*(supply_freq + base_freq)*t)
                    signal += 0.3 * np.sin(2*np.pi*(supply_freq - base_freq)*t)
                    signal += 0.1*np.random.randn(signal_length)
                    
                elif fault_type == 'Rotor_Bar_Fault':
                    # 转子条故障：低频调制
                    modulation_freq = 2 * base_freq
                    signal += 0.4 * np.sin(2*np.pi*50*t) * (1 + 0.5*np.sin(2*np.pi*modulation_freq*t))
                    signal += 0.1*np.random.randn(signal_length)
                    
                elif fault_type == 'Misalignment':
                    # 不对中：2倍频和3倍频
                    signal += 0.3 * np.sin(2*np.pi*2*base_freq*t)
                    signal += 0.2 * np.sin(2*np.pi*3*base_freq*t)
                    signal += 0.1*np.random.randn(signal_length)
                    
                elif fault_type == 'Unbalance':
                    # 不平衡：1倍频增强
                    signal += 0.5 * np.sin(2*np.pi*base_freq*t + np.random.uniform(0, 2*np.pi))
                    signal += 0.1*np.random.randn(signal_length)
                    
                elif 'Gear' in fault_type:
                    gear_mesh_freq = 12 * base_freq  # 假设12齿齿轮
                    if 'Tooth' in fault_type:
                        # 齿轮齿故障
                        signal += 0.4 * np.sin(2*np.pi*gear_mesh_freq*t)
                        signal += 0.2 * np.sin(2*np.pi*(gear_mesh_freq + base_freq)*t)
                    else:  # Wear
                        # 齿轮磨损
                        signal += 0.3 * np.sin(2*np.pi*gear_mesh_freq*t) * (1 + 0.2*np.sin(2*np.pi*base_freq*t))
                    signal += 0.1*np.random.randn(signal_length)
                    
                else:
                    # 其他故障类型
                    fault_freq = np.random.uniform(80, 200)
                    signal += 0.3 * np.sin(2*np.pi*fault_freq*t)
                    signal += 0.1*np.random.randn(signal_length)
                
                X_data.append(signal)
                y_data.append(class_idx)
        
        X_data = np.array(X_data)
        y_data = np.array(y_data)
        
        print(f"合成数据集创建完成: {X_data.shape}")
        print(f"故障类型: {fault_types}")
        
        return X_data, y_data, fault_types
    
    def combine_datasets(self, datasets):
        """合并多个数据集"""
        print("合并数据集...")
        
        all_X = []
        all_y = []
        all_fault_types = []
        
        current_class_offset = 0
        
        for X, y, fault_types in datasets:
            # 调整标签偏移
            y_adjusted = y + current_class_offset
            
            all_X.append(X)
            all_y.append(y_adjusted)
            all_fault_types.extend(fault_types)
            
            current_class_offset += len(fault_types)
        
        combined_X = np.vstack(all_X)
        combined_y = np.hstack(all_y)
        
        print(f"合并后数据集: {combined_X.shape}")
        print(f"总故障类型数: {len(all_fault_types)}")
        
        return combined_X, combined_y, all_fault_types

def main():
    """测试外部数据集加载器"""
    loader = ExternalDatasetLoader()
    
    # 加载不同的数据集
    print("=" * 60)
    print("外部数据集加载测试")
    print("=" * 60)
    
    # 1. 加载CWRU数据集
    cwru_X, cwru_y, cwru_types = loader.load_cwru_dataset()
    
    # 2. 加载Paderborn数据集
    paderborn_X, paderborn_y, paderborn_types = loader.load_paderborn_dataset()
    
    # 3. 创建合成数据集
    synthetic_X, synthetic_y, synthetic_types = loader.create_synthetic_motor_dataset()
    
    # 4. 合并所有数据集
    combined_X, combined_y, combined_types = loader.combine_datasets([
        (cwru_X, cwru_y, cwru_types),
        (paderborn_X, paderborn_y, paderborn_types),
        (synthetic_X, synthetic_y, synthetic_types)
    ])
    
    print(f"\n最终合并数据集: {combined_X.shape}")
    print(f"类别数: {len(combined_types)}")
    
    return combined_X, combined_y, combined_types

if __name__ == "__main__":
    X, y, fault_types = main()
