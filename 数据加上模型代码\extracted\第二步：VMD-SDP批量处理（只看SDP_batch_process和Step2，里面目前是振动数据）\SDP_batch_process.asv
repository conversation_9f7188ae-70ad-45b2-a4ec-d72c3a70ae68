
clc
clear

%% 工况0数据转换为连续小波变换时频图

%%  生成图像
% file_path 一定要给文件路径
file_path ='工况1-SDP'; %
% 判断文件路径是否存在，如果文件路径不存在，则创建
if ~exist(file_path, 'dir')
    mkdir(file_path);
    fprintf('文件路径 %s 创建成功！\n', file_path);
end
load shuju/data_total_6lei.mat
%% 故障1
f=data(2,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])

%计算 SDP
for g=1:120 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end

%% 故障2
f=data(122,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=121:240 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
%% 故障3
f=data(242,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=241:360 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
%% 故障4
f=data(362,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=361:480 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
%% 故障5
f=data(482,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=481:600 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
%% 故障6
f=data(602,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=601:720 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
















%% 工况2
%% file_path 一定要给文件路径
file_path ='工况2-SDP'; %
% 判断文件路径是否存在，如果文件路径不存在，则创建
if ~exist(file_path, 'dir')
    mkdir(file_path);
    fprintf('文件路径 %s 创建成功！\n', file_path);
end
load data_total_6lei_2.mat
%% 故障1
f=data(2,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])

%计算 SDP
for g=1:120 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end

%% 故障2
f=data(122,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=121:240 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
%% 故障3
f=data(242,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=241:360 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
%% 故障4
f=data(362,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=361:480 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
%% 故障5
f=data(482,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=481:600 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end
%% 故障6
f=data(602,:);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
    % 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%计算 SDP
for g=601:720 %size(data,1)
    disp(['正在将工况1-SDP：',num2str(g),'/',num2str(size(data,1))])
f=data(g,:);
DE6 = f;
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
  imf_SDP(imf);
  set(gcf,'Units','centimeter','Position',[600 100 3.8 3.8]);
  set(gca,'position',[0 0 1 1]) % 无边
  box off; axis off % 无框
  saveas(gcf,fullfile(file_path, [num2str(g) '.jpg'])) % 保存
  hold off
end




