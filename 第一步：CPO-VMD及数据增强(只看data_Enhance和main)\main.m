clear all
close all
clc
warning off

% %% 加载信号
%% 电流数据载入
%REVD转子励磁电压断开
%OP缺相
%VREC转子励磁电流变化
%2PSC两相短路
%1PSC单相对中性点短路
%NF无故障
load shuju/data6.mat;           %4
x_input=data(985,:);%550   705
N=length(x_input);              %数据长度
fs = 3000; %采样频率
n=0:N-1;  
t=n/fs; %生成每个数据点对应的时间戳
y=x_input;
figure(1); %时域图像
subplot(2,1,1);
plot(t,y);
xlabel('时间');
ylabel('幅值');
signal=x_input;
%% CPO-VMD分解
%% 参数设置
data=signal;
len=length(data);
f=data(1:len);
% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
 
%% 普通VMD分解
%[u, u_hat, omega] = VMD(f, alpha, tau, K, DC, init, tol);
% 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
figure
plot(curve,'linewidth',1.5);
title('收敛曲线')
xlabel('迭代次数')
ylabel('适应度值')
grid on

%分解
figure
subplot(size(u1,1)+1,1,1);
plot(f,'k');grid on;
title('原始数据');
for i = 1:size(u1,1)
    subplot(size(u1,1)+1,1,i+1);
    plot(u1(i,:),'k');
end

disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])

% 3d
imf=u1;
figure
set(gcf,'unit','normalized','position',[0.2,0.3,0.5,0.45]);
x = 1:size(imf,1);
y = 1:size(imf,2);
z = imf(x,:);
[X,Y]=meshgrid(x,y);
plot3(X,Y,z)
grid on
xticklabel = {};
for ii=1:size(imf,1)
    
    xticklabel{ii}= ['IMF' num2str(ii)];
    
end
set(gca,'xtick',1:1:size(imf,1),'XTickLabel',xticklabel);
view(-20, 50); %视角
%% 计时结束
%% 频域图
[m,n]=size(u1);
imf=u1;
len=length(imf);%信号长度
fs=2049;%采样频率
% 采样时间
t = (0:len-1)/fs; 
figure
for i=1:m
subplot(m,1,i)
[cc,y_f]=hua_fft_1(imf(i,:),fs,1);
a100(i,:)=cc;
plot(y_f,cc,'k','LineWIdth',1.5);
% hua_fft_1(u(i,:),fs,1)
ylabel(['imf',num2str(i)]);
axis tight
end
xlabel('频率/Hz')


%% 导入数据
% load data_total_6lei.mat    % 导入外圈@6_0数据 
% DE6 = data(1,:);
DE6 = signal;
alpha =Target_pos(1);       % moderate bandwidth constraint：适度的带宽约束/惩罚因子
tau = 0;          % noise-tolerance (no strict fidelity enforcement)：噪声容限）
K = Target_pos(2);              % modes：分解的模态数
DC = 0;             % no DC part imposed：无直流部分
init = 1;           % initialize omegas uniformly  ：omegas的均匀初始化
tol = 1e-7;     
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
[imf, u_hat, omega] = VMD(DE6, alpha, tau, K, DC, init, tol);
imf = imf';  % 数据转秩
%% SDP参数
imf_num = size(imf,2); % imf分量个数
F = 360/imf_num; % 镜像对称平面旋转角
L = 2;  % 时间间隔参数 （取[1,10]之间可以表示信号之间的细微差别                     
G = F/2; % 放大角度（一般小于等于F镜像对称平面旋转角

%% SDP绘制
figure
color_map = hsv(imf_num*2); % 使用hsv颜色映射
marker_size = 10; % 符号大小
for i = 1:imf_num
    data = imf(:,i);
    rs = (data - min(data))/(max(data) - min(data)); % 极坐标半径
    th = F*i + rs*G; % 逆时针转角
    ph = F*i - rs*G; % 顺时针转角
    th = th(L:end);
    ph = ph(L:end);
    rs = rs(1:end-L+1);
    color_index = i; % 计算颜色索引
    polarscatter(th*(pi/180), rs, marker_size, 'filled', 'MarkerFaceColor', color_map(color_index,:));
    hold on;
    polarscatter(ph*(pi/180), rs, marker_size, 'filled', 'MarkerFaceColor', color_map(color_index,:));
    title('fusion data')
end 
