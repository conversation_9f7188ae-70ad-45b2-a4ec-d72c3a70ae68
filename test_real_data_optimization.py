"""
使用真实故障电流数据测试参数优化功能
"""

from fault_diagnosis_system import FaultDiagnosisSystem
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import numpy as np
import matplotlib.pyplot as plt

def test_real_data_optimization():
    """使用真实数据测试参数优化"""
    print("=" * 60)
    print("真实故障电流数据参数优化测试")
    print("=" * 60)
    
    # 初始化系统
    system = FaultDiagnosisSystem()
    
    try:
        # 1. 加载真实故障数据
        print("\n1. 加载真实故障电流数据")
        print("-" * 40)
        
        X, y, fault_types = system.generate_sample_data(
            num_classes=13, 
            samples_per_class=5,  # 少量样本用于测试
            signal_length=1024,   # 适中长度
            use_external=False    # 只使用真实数据
        )
        
        print(f"加载的数据形状: {X.shape}")
        print(f"故障类型: {fault_types}")
        
        # 选择一个真实的故障信号进行测试
        # 选择第一个非正常状态的信号
        test_signal = None
        test_fault_type = None
        
        for i, fault_type in enumerate(fault_types):
            if 'Normal' not in fault_type:
                class_mask = y == i
                if np.any(class_mask):
                    test_signal = X[class_mask][0]  # 取第一个样本
                    test_fault_type = fault_type
                    break
        
        if test_signal is None:
            print("未找到合适的故障信号，使用第一个信号")
            test_signal = X[0]
            test_fault_type = fault_types[y[0]]
        
        print(f"选择测试信号: {test_fault_type}")
        print(f"信号统计: 均值={np.mean(test_signal):.4f}, 标准差={np.std(test_signal):.4f}")
        
        # 2. 参数优化测试
        print("\n2. VMD参数优化（真实数据）")
        print("-" * 40)
        
        print("优化前默认参数:")
        print(f"  alpha = {system.vmd_decomposer.alpha}")
        print(f"  K = {system.vmd_decomposer.K}")
        
        # 执行参数优化
        best_params, conv_curve = system.optimize_vmd_parameters(test_signal)
        
        print("\n优化后参数:")
        print(f"  alpha = {system.vmd_decomposer.alpha}")
        print(f"  K = {system.vmd_decomposer.K}")
        
        # 3. 比较优化前后效果
        print("\n3. 比较优化前后VMD分解效果")
        print("-" * 40)
        
        # 使用默认参数分解
        from fault_diagnosis_system import VMDDecomposer
        default_vmd = VMDDecomposer(alpha=2000, K=4)
        u_default, _, _ = default_vmd.vmd(test_signal)
        
        # 使用优化参数分解
        u_optimized, _, _ = system.vmd_decomposer.vmd(test_signal)
        
        print("默认参数分解结果:")
        print(f"  模态数: {u_default.shape[0]}")
        default_recon_error = np.mean((test_signal - np.sum(u_default, axis=0))**2)
        print(f"  重构误差: {default_recon_error:.6f}")
        
        print("优化参数分解结果:")
        print(f"  模态数: {u_optimized.shape[0]}")
        optimized_recon_error = np.mean((test_signal - np.sum(u_optimized, axis=0))**2)
        print(f"  重构误差: {optimized_recon_error:.6f}")
        
        # 4. 绘制真实数据优化效果图
        print("\n4. 绘制真实数据优化效果图")
        print("-" * 40)
        
        plt.figure(figsize=(15, 10))
        
        # 收敛曲线
        plt.subplot(3, 2, 1)
        plt.plot(conv_curve, 'b-', linewidth=2, marker='o')
        plt.title('CPO优化收敛曲线（真实数据）')
        plt.xlabel('迭代次数')
        plt.ylabel('适应度值')
        plt.grid(True)
        
        # 原始故障信号
        plt.subplot(3, 2, 2)
        t = np.linspace(0, len(test_signal)/1000, len(test_signal))  # 假设采样率1kHz
        plt.plot(t, test_signal, 'k-', linewidth=1)
        plt.title(f'原始故障信号: {test_fault_type}')
        plt.xlabel('时间 (s)')
        plt.ylabel('电流 (A)')
        plt.grid(True)
        
        # 默认参数分解结果
        plt.subplot(3, 2, 3)
        for i in range(min(4, u_default.shape[0])):
            plt.plot(t, u_default[i], label=f'IMF{i+1}', alpha=0.8)
        plt.title(f'默认参数VMD分解 (α={2000}, K={4})')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅值')
        plt.legend()
        plt.grid(True)
        
        # 优化参数分解结果
        plt.subplot(3, 2, 4)
        for i in range(min(4, u_optimized.shape[0])):
            plt.plot(t, u_optimized[i], label=f'IMF{i+1}', alpha=0.8)
        plt.title(f'优化参数VMD分解 (α={system.vmd_decomposer.alpha}, K={system.vmd_decomposer.K})')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅值')
        plt.legend()
        plt.grid(True)
        
        # 频谱对比
        plt.subplot(3, 2, 5)
        freqs = np.fft.fftfreq(len(test_signal), 1/1000)  # 假设采样率1kHz
        fft_signal = np.abs(np.fft.fft(test_signal))
        plt.plot(freqs[:len(freqs)//2], fft_signal[:len(freqs)//2], 'k-', linewidth=2)
        plt.title('原始信号频谱')
        plt.xlabel('频率 (Hz)')
        plt.ylabel('幅值')
        plt.grid(True)
        
        # 重构信号对比
        plt.subplot(3, 2, 6)
        recon_default = np.sum(u_default, axis=0)
        recon_optimized = np.sum(u_optimized, axis=0)
        
        plt.plot(t, test_signal, 'k-', label='原始信号', linewidth=2, alpha=0.7)
        plt.plot(t, recon_default, 'r--', label='默认参数重构', linewidth=1.5)
        plt.plot(t, recon_optimized, 'b--', label='优化参数重构', linewidth=1.5)
        plt.title('信号重构对比')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅值')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('real_data_optimization_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("真实数据优化效果图已保存为: real_data_optimization_results.png")
        
        # 5. 详细评估
        print("\n5. 真实数据优化效果评估")
        print("-" * 40)
        
        # 计算评价指标
        default_orthogonality = system.calculate_orthogonality(u_default)
        optimized_orthogonality = system.calculate_orthogonality(u_optimized)
        
        default_freq_sep = system.calculate_frequency_separation(u_default)
        optimized_freq_sep = system.calculate_frequency_separation(u_optimized)
        
        print("详细评价指标:")
        print(f"正交性 (越小越好):")
        print(f"  默认参数: {default_orthogonality:.6f}")
        print(f"  优化参数: {optimized_orthogonality:.6f}")
        
        if default_orthogonality > 0:
            ortho_improvement = (default_orthogonality - optimized_orthogonality) / default_orthogonality * 100
            print(f"  改善: {ortho_improvement:+.2f}%")
        
        print(f"频域分离度 (越大越好):")
        print(f"  默认参数: {default_freq_sep:.6f}")
        print(f"  优化参数: {optimized_freq_sep:.6f}")
        
        if default_freq_sep > 0:
            freq_improvement = (optimized_freq_sep - default_freq_sep) / default_freq_sep * 100
            print(f"  改善: {freq_improvement:+.2f}%")
        
        print(f"重构误差 (越小越好):")
        print(f"  默认参数: {default_recon_error:.6f}")
        print(f"  优化参数: {optimized_recon_error:.6f}")
        
        if default_recon_error > 0:
            recon_improvement = (default_recon_error - optimized_recon_error) / default_recon_error * 100
            print(f"  改善: {recon_improvement:+.2f}%")
        
        # 总体评估
        improvements = 0
        if optimized_orthogonality < default_orthogonality:
            improvements += 1
        if optimized_freq_sep > default_freq_sep:
            improvements += 1
        if optimized_recon_error < default_recon_error:
            improvements += 1
            
        print(f"\n真实数据优化总体评估: {improvements}/3 个指标得到改善")
        
        if improvements >= 2:
            print("🎉 真实数据参数优化效果显著！")
        elif improvements >= 1:
            print("👍 真实数据参数优化有一定效果")
        else:
            print("⚠️ 真实数据参数优化效果不明显")
        
        return {
            'optimization_successful': True,
            'fault_type': test_fault_type,
            'best_params': best_params,
            'improvements': improvements,
            'default_recon_error': default_recon_error,
            'optimized_recon_error': optimized_recon_error
        }
        
    except Exception as e:
        print(f"真实数据参数优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'optimization_successful': False,
            'error': str(e)
        }

def main():
    """主测试函数"""
    print("开始真实故障电流数据参数优化测试...")
    
    results = test_real_data_optimization()
    
    print("\n" + "=" * 60)
    print("真实数据测试完成！")
    print("=" * 60)
    
    if results['optimization_successful']:
        print("✅ 真实数据参数优化功能正常工作")
        print(f"✅ 测试故障类型: {results['fault_type']}")
        print(f"✅ 优化改善了 {results['improvements']}/3 个评价指标")
        print(f"✅ 重构误差变化: {results['default_recon_error']:.6f} → {results['optimized_recon_error']:.6f}")
    else:
        print("❌ 真实数据参数优化功能存在问题")
        print(f"❌ 错误信息: {results.get('error', '未知错误')}")
    
    return results

if __name__ == "__main__":
    main()
