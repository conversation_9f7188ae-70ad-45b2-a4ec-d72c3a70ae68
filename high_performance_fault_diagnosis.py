"""
高性能六相永磁同步电机故障诊断系统
目标: 将准确率提升到95%以上
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
from scipy.signal import butter, filtfilt
import warnings
warnings.filterwarnings('ignore')

class AdvancedDataAugmentation:
    """高级数据增强器"""
    
    def __init__(self):
        self.augmentation_methods = [
            self.add_gaussian_noise,
            self.time_warping,
            self.amplitude_scaling,
            self.frequency_masking,
            self.time_shifting,
            self.signal_mixing
        ]
    
    def add_gaussian_noise(self, signal, noise_factor=0.02):
        """添加高斯噪声"""
        noise = np.random.normal(0, noise_factor * np.std(signal), signal.shape)
        return signal + noise
    
    def time_warping(self, signal, stretch_factor=None):
        """时间扭曲"""
        if stretch_factor is None:
            stretch_factor = np.random.uniform(0.8, 1.2)
        
        original_length = len(signal)
        new_length = int(original_length * stretch_factor)
        
        # 重采样
        indices = np.linspace(0, original_length - 1, new_length)
        warped_signal = np.interp(indices, np.arange(original_length), signal)
        
        # 调整回原始长度
        if new_length > original_length:
            return warped_signal[:original_length]
        else:
            padded = np.pad(warped_signal, (0, original_length - new_length), mode='edge')
            return padded
    
    def amplitude_scaling(self, signal, scale_factor=None):
        """幅值缩放"""
        if scale_factor is None:
            scale_factor = np.random.uniform(0.8, 1.2)
        return signal * scale_factor
    
    def frequency_masking(self, signal, mask_ratio=0.1):
        """频域掩码"""
        fft_signal = np.fft.fft(signal)
        mask_length = int(len(fft_signal) * mask_ratio)
        start_idx = np.random.randint(0, len(fft_signal) - mask_length)
        
        masked_fft = fft_signal.copy()
        masked_fft[start_idx:start_idx + mask_length] = 0
        
        return np.real(np.fft.ifft(masked_fft))
    
    def time_shifting(self, signal, shift_ratio=None):
        """时间偏移"""
        if shift_ratio is None:
            shift_ratio = np.random.uniform(-0.1, 0.1)
        
        shift_samples = int(len(signal) * shift_ratio)
        return np.roll(signal, shift_samples)
    
    def signal_mixing(self, signal, mix_signal=None, mix_ratio=0.1):
        """信号混合"""
        if mix_signal is None:
            # 生成随机信号进行混合
            mix_signal = np.random.normal(0, np.std(signal) * 0.1, signal.shape)
        
        return (1 - mix_ratio) * signal + mix_ratio * mix_signal
    
    def augment_dataset(self, X, y, augmentation_factor=5):
        """数据集增强"""
        print(f"开始数据增强，增强因子: {augmentation_factor}")
        
        augmented_X = []
        augmented_y = []
        
        # 保留原始数据
        augmented_X.extend(X)
        augmented_y.extend(y)
        
        # 为每个样本生成增强数据
        for i, (signal, label) in enumerate(zip(X, y)):
            for _ in range(augmentation_factor):
                # 随机选择增强方法
                aug_method = np.random.choice(self.augmentation_methods)
                augmented_signal = aug_method(signal.copy())
                
                # 确保信号长度一致
                if len(augmented_signal) != len(signal):
                    augmented_signal = np.resize(augmented_signal, len(signal))
                
                augmented_X.append(augmented_signal)
                augmented_y.append(label)
            
            if (i + 1) % 50 == 0:
                print(f"已处理 {i + 1}/{len(X)} 个样本")
        
        augmented_X = np.array(augmented_X)
        augmented_y = np.array(augmented_y)
        
        print(f"数据增强完成: {X.shape} -> {augmented_X.shape}")
        return augmented_X, augmented_y

class SyntheticDataGenerator:
    """合成数据生成器"""
    
    def __init__(self, sampling_rate=1000):
        self.sampling_rate = sampling_rate
        
    def generate_fault_signal(self, fault_type, signal_length, base_freq=50):
        """生成特定故障类型的合成信号"""
        t = np.linspace(0, signal_length / self.sampling_rate, signal_length)
        
        # 基础信号
        base_signal = np.sin(2 * np.pi * base_freq * t)
        
        if fault_type == 'Normal':
            # 正常信号：主要是基础频率
            signal = base_signal + 0.1 * np.random.randn(signal_length)
            
        elif 'A相增益' in fault_type:
            # 电流传感器增益故障
            gain_factor = 1.5 if '1.5' in fault_type else 0.0
            signal = gain_factor * base_signal + 0.1 * np.random.randn(signal_length)
            
        elif 'A相漂移' in fault_type:
            # 电流传感器漂移故障
            drift = 5.0 if '5' in fault_type else 0.0
            signal = base_signal + drift + 0.1 * np.random.randn(signal_length)
            
        elif 'duan' in fault_type:
            # 开关管断路故障
            fault_freq = np.random.uniform(100, 200)
            signal = base_signal + 0.5 * np.sin(2 * np.pi * fault_freq * t)
            signal += 0.15 * np.random.randn(signal_length)
            
        elif 'and' in fault_type:
            # 多开关管故障
            fault_freq1 = np.random.uniform(80, 120)
            fault_freq2 = np.random.uniform(150, 200)
            signal = base_signal + 0.3 * np.sin(2 * np.pi * fault_freq1 * t)
            signal += 0.3 * np.sin(2 * np.pi * fault_freq2 * t)
            signal += 0.15 * np.random.randn(signal_length)
            
        else:
            # 默认故障信号
            fault_freq = np.random.uniform(100, 300)
            signal = base_signal + 0.4 * np.sin(2 * np.pi * fault_freq * t)
            signal += 0.1 * np.random.randn(signal_length)
        
        return signal
    
    def generate_synthetic_dataset(self, fault_types, samples_per_class, signal_length):
        """生成合成数据集"""
        print("生成合成数据集...")
        
        X_synthetic = []
        y_synthetic = []
        
        for class_idx, fault_type in enumerate(fault_types):
            print(f"生成 {fault_type} 合成数据: {samples_per_class} 个样本")
            
            for _ in range(samples_per_class):
                synthetic_signal = self.generate_fault_signal(fault_type, signal_length)
                X_synthetic.append(synthetic_signal)
                y_synthetic.append(class_idx)
        
        return np.array(X_synthetic), np.array(y_synthetic)

class ImprovedSSETransformer(nn.Module):
    """改进的SSE-Transformer模型"""
    
    def __init__(self, input_size, num_classes, d_model=512, nhead=8, num_layers=6, 
                 dim_feedforward=2048, dropout=0.1):
        super(ImprovedSSETransformer, self).__init__()
        
        # 输入投影
        self.input_projection = nn.Linear(input_size, d_model)
        
        # 位置编码
        self.pos_encoding = self._generate_positional_encoding(d_model, 5000)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # SE注意力机制
        self.se_attention = SEAttention(d_model)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, num_classes)
        )
        
        # 初始化权重
        self._init_weights()
    
    def _generate_positional_encoding(self, d_model, max_len):
        """生成位置编码"""
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        return pe.unsqueeze(0)
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x):
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        seq_len = x.size(1)
        if seq_len <= self.pos_encoding.size(1):
            x = x + self.pos_encoding[:, :seq_len, :].to(x.device)
        
        # Transformer编码
        x = self.transformer_encoder(x)
        
        # SE注意力
        x = self.se_attention(x)
        
        # 全局平均池化
        x = torch.mean(x, dim=1)
        
        # 分类
        output = self.classifier(x)
        
        return output

class SEAttention(nn.Module):
    """SE注意力机制"""
    
    def __init__(self, d_model, reduction=16):
        super(SEAttention, self).__init__()
        self.global_avg_pool = nn.AdaptiveAvgPool1d(1)
        self.fc = nn.Sequential(
            nn.Linear(d_model, d_model // reduction),
            nn.ReLU(inplace=True),
            nn.Linear(d_model // reduction, d_model),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # x shape: (batch_size, seq_len, d_model)
        b, s, c = x.size()
        
        # 全局平均池化
        y = self.global_avg_pool(x.transpose(1, 2)).view(b, c)
        
        # SE模块
        y = self.fc(y).view(b, 1, c)
        
        # 应用注意力权重
        return x * y.expand_as(x)

class HighPerformanceFaultDiagnosis:
    """高性能故障诊断系统"""
    
    def __init__(self, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.data_augmenter = AdvancedDataAugmentation()
        self.synthetic_generator = SyntheticDataGenerator()
        
        print(f"使用设备: {self.device}")
    
    def prepare_enhanced_dataset(self, X_real, y_real, fault_types, 
                               augmentation_factor=8, synthetic_ratio=0.3):
        """准备增强数据集"""
        print("=" * 60)
        print("准备增强数据集")
        print("=" * 60)
        
        # 1. 数据增强
        print("\n1. 数据增强")
        X_augmented, y_augmented = self.data_augmenter.augment_dataset(
            X_real, y_real, augmentation_factor
        )
        
        # 2. 生成合成数据
        print("\n2. 生成合成数据")
        synthetic_samples = int(len(X_augmented) * synthetic_ratio)
        samples_per_class = synthetic_samples // len(fault_types)
        
        X_synthetic, y_synthetic = self.synthetic_generator.generate_synthetic_dataset(
            fault_types, samples_per_class, X_real.shape[1]
        )
        
        # 3. 合并数据集
        print("\n3. 合并数据集")
        X_combined = np.vstack([X_augmented, X_synthetic])
        y_combined = np.hstack([y_augmented, y_synthetic])
        
        print(f"最终数据集大小: {X_combined.shape}")
        print(f"各类别样本数量:")
        unique_labels, counts = np.unique(y_combined, return_counts=True)
        for label, count in zip(unique_labels, counts):
            if label < len(fault_types):
                print(f"  {fault_types[label]}: {count} 样本")
        
        return X_combined, y_combined
    
    def advanced_feature_extraction(self, X):
        """高级特征提取"""
        print("执行高级特征提取...")
        
        features = []
        
        for signal in X:
            # 时域特征
            time_features = self._extract_time_features(signal)
            
            # 频域特征
            freq_features = self._extract_frequency_features(signal)
            
            # 时频域特征
            tf_features = self._extract_time_frequency_features(signal)
            
            # 合并特征
            combined_features = np.concatenate([time_features, freq_features, tf_features])
            features.append(combined_features)
        
        return np.array(features)
    
    def _extract_time_features(self, signal):
        """提取时域特征"""
        features = []
        
        # 统计特征
        features.extend([
            np.mean(signal),           # 均值
            np.std(signal),            # 标准差
            np.var(signal),            # 方差
            np.max(signal),            # 最大值
            np.min(signal),            # 最小值
            np.ptp(signal),            # 峰峰值
            np.mean(np.abs(signal)),   # 平均绝对值
            np.sqrt(np.mean(signal**2)) # RMS
        ])
        
        # 形状特征
        features.extend([
            self._skewness(signal),    # 偏度
            self._kurtosis(signal),    # 峰度
            self._crest_factor(signal), # 波峰因子
            self._impulse_factor(signal) # 脉冲因子
        ])
        
        return np.array(features)
    
    def _extract_frequency_features(self, signal):
        """提取频域特征"""
        # FFT
        fft_signal = np.fft.fft(signal)
        magnitude = np.abs(fft_signal[:len(signal)//2])
        freqs = np.fft.fftfreq(len(signal), 1.0)[:len(signal)//2]
        
        features = []
        
        # 频域统计特征
        features.extend([
            np.mean(magnitude),        # 频域均值
            np.std(magnitude),         # 频域标准差
            np.max(magnitude),         # 最大幅值
            freqs[np.argmax(magnitude)] # 主频率
        ])
        
        # 功率谱密度特征
        psd = magnitude ** 2
        total_power = np.sum(psd)
        
        # 频带能量比
        low_freq_power = np.sum(psd[freqs < 0.1])
        mid_freq_power = np.sum(psd[(freqs >= 0.1) & (freqs < 0.3)])
        high_freq_power = np.sum(psd[freqs >= 0.3])
        
        features.extend([
            low_freq_power / total_power,
            mid_freq_power / total_power,
            high_freq_power / total_power
        ])
        
        return np.array(features)
    
    def _extract_time_frequency_features(self, signal):
        """提取时频域特征"""
        # 小波变换特征
        from scipy import signal as scipy_signal
        
        # 连续小波变换
        widths = np.arange(1, 31)
        cwt_matrix = scipy_signal.cwt(signal, scipy_signal.ricker, widths)
        
        features = []
        
        # 小波能量特征
        for i in range(0, len(widths), 5):
            scale_energy = np.sum(np.abs(cwt_matrix[i:i+5])**2)
            features.append(scale_energy)
        
        # 瞬时特征
        analytic_signal = scipy_signal.hilbert(signal)
        instantaneous_amplitude = np.abs(analytic_signal)
        instantaneous_frequency = np.diff(np.unwrap(np.angle(analytic_signal)))
        
        features.extend([
            np.mean(instantaneous_amplitude),
            np.std(instantaneous_amplitude),
            np.mean(instantaneous_frequency),
            np.std(instantaneous_frequency)
        ])
        
        return np.array(features)
    
    def _skewness(self, signal):
        """计算偏度"""
        mean = np.mean(signal)
        std = np.std(signal)
        return np.mean(((signal - mean) / std) ** 3) if std > 0 else 0
    
    def _kurtosis(self, signal):
        """计算峰度"""
        mean = np.mean(signal)
        std = np.std(signal)
        return np.mean(((signal - mean) / std) ** 4) - 3 if std > 0 else 0
    
    def _crest_factor(self, signal):
        """计算波峰因子"""
        rms = np.sqrt(np.mean(signal**2))
        return np.max(np.abs(signal)) / rms if rms > 0 else 0
    
    def _impulse_factor(self, signal):
        """计算脉冲因子"""
        mean_abs = np.mean(np.abs(signal))
        return np.max(np.abs(signal)) / mean_abs if mean_abs > 0 else 0

    def train_high_performance_model(self, X, y, fault_types, test_size=0.2,
                                   epochs=200, batch_size=32, learning_rate=0.0001):
        """训练高性能模型"""
        print("=" * 60)
        print("训练高性能故障诊断模型")
        print("=" * 60)

        # 1. 准备增强数据集
        X_enhanced, y_enhanced = self.prepare_enhanced_dataset(X, y, fault_types)

        # 2. 特征提取
        print("\n4. 高级特征提取")
        X_features = self.advanced_feature_extraction(X_enhanced)

        # 3. 数据预处理
        print("\n5. 数据预处理")
        X_scaled = self.scaler.fit_transform(X_features)
        y_encoded = self.label_encoder.fit_transform(y_enhanced)

        # 4. 数据分割
        print("\n6. 数据分割")
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y_encoded, test_size=test_size,
            random_state=42, stratify=y_encoded
        )

        print(f"训练集: {X_train.shape}")
        print(f"测试集: {X_test.shape}")

        # 5. 创建数据加载器
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train).unsqueeze(1),  # 添加序列维度
            torch.LongTensor(y_train)
        )
        test_dataset = TensorDataset(
            torch.FloatTensor(X_test).unsqueeze(1),
            torch.LongTensor(y_test)
        )

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

        # 6. 创建模型
        print("\n7. 创建改进模型")
        input_size = X_features.shape[1]
        num_classes = len(np.unique(y_encoded))

        self.model = ImprovedSSETransformer(
            input_size=input_size,
            num_classes=num_classes,
            d_model=512,
            nhead=8,
            num_layers=6,
            dim_feedforward=2048,
            dropout=0.1
        ).to(self.device)

        # 7. 设置优化器和损失函数
        optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=1e-4
        )

        # 使用标签平滑的交叉熵损失
        criterion = LabelSmoothingCrossEntropy(smoothing=0.1)

        # 学习率调度器
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=20, T_mult=2, eta_min=1e-6
        )

        # 8. 训练模型
        print("\n8. 开始训练")
        train_losses, train_accs, val_accs = self._train_model(
            train_loader, test_loader, criterion, optimizer, scheduler, epochs
        )

        # 9. 最终评估
        print("\n9. 最终评估")
        final_accuracy = self.evaluate_model(X_test, y_test, fault_types)

        return {
            'model': self.model,
            'final_accuracy': final_accuracy,
            'train_losses': train_losses,
            'train_accs': train_accs,
            'val_accs': val_accs,
            'fault_types': fault_types
        }

    def _train_model(self, train_loader, val_loader, criterion, optimizer,
                    scheduler, epochs):
        """训练模型"""
        train_losses = []
        train_accs = []
        val_accs = []

        best_val_acc = 0.0
        patience = 20
        patience_counter = 0

        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(self.device), target.to(self.device)

                optimizer.zero_grad()
                output = self.model(data)
                loss = criterion(output, target)
                loss.backward()

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                optimizer.step()

                train_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                train_total += target.size(0)
                train_correct += (predicted == target).sum().item()

            # 验证阶段
            self.model.eval()
            val_correct = 0
            val_total = 0

            with torch.no_grad():
                for data, target in val_loader:
                    data, target = data.to(self.device), target.to(self.device)
                    output = self.model(data)
                    _, predicted = torch.max(output.data, 1)
                    val_total += target.size(0)
                    val_correct += (predicted == target).sum().item()

            # 计算准确率
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            avg_train_loss = train_loss / len(train_loader)

            train_losses.append(avg_train_loss)
            train_accs.append(train_acc)
            val_accs.append(val_acc)

            # 学习率调度
            scheduler.step()

            # 早停检查
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_model.pth')
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f'早停触发，在第 {epoch+1} 轮停止训练')
                break

            if (epoch + 1) % 10 == 0:
                print(f'Epoch [{epoch+1}/{epochs}]')
                print(f'Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc:.2f}%')
                print(f'Val Acc: {val_acc:.2f}%, Best Val Acc: {best_val_acc:.2f}%')
                print(f'学习率: {optimizer.param_groups[0]["lr"]:.6f}')
                print('-' * 50)

        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_model.pth'))
        print(f'训练完成！最佳验证准确率: {best_val_acc:.2f}%')

        return train_losses, train_accs, val_accs

    def evaluate_model(self, X_test, y_test, fault_types):
        """评估模型"""
        self.model.eval()

        # 预处理测试数据
        X_test_features = self.advanced_feature_extraction(X_test.reshape(len(X_test), -1))
        X_test_scaled = self.scaler.transform(X_test_features)

        # 预测
        with torch.no_grad():
            test_data = torch.FloatTensor(X_test_scaled).unsqueeze(1).to(self.device)
            outputs = self.model(test_data)
            _, predicted = torch.max(outputs, 1)
            predicted = predicted.cpu().numpy()

        # 计算准确率
        y_test_encoded = self.label_encoder.transform(y_test)
        accuracy = accuracy_score(y_test_encoded, predicted) * 100

        # 绘制混淆矩阵
        cm = confusion_matrix(y_test_encoded, predicted)

        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=fault_types, yticklabels=fault_types)
        plt.title(f'混淆矩阵 (准确率: {accuracy:.2f}%)')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig('high_performance_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 打印分类报告
        print("\n分类报告:")
        print(classification_report(y_test_encoded, predicted, target_names=fault_types))

        return accuracy

class LabelSmoothingCrossEntropy(nn.Module):
    """标签平滑交叉熵损失"""

    def __init__(self, smoothing=0.1):
        super(LabelSmoothingCrossEntropy, self).__init__()
        self.smoothing = smoothing

    def forward(self, pred, target):
        num_classes = pred.size(-1)
        log_pred = torch.log_softmax(pred, dim=-1)

        # 如果只有一个类别，使用标准交叉熵
        if num_classes <= 1:
            return torch.nn.functional.cross_entropy(pred, target)

        # 创建平滑标签
        smooth_target = torch.zeros_like(log_pred)
        smooth_target.fill_(self.smoothing / (num_classes - 1))
        smooth_target.scatter_(1, target.unsqueeze(1), 1.0 - self.smoothing)

        return torch.mean(torch.sum(-smooth_target * log_pred, dim=-1))
