%%  清空环境变量 
warning off             % 关闭报警信息
close all               % 关闭开启的图窗
clear                   % 清空变量
clc  
addpath(genpath(pwd))

load Resize_Working_condition1.mat %加载数据集
resizeimg = Resize_Working_condition1';
input=resizeimg(:,1);
output=resizeimg(:,2);
numClasses = length(unique(cell2mat(output))); %分类个数
jg = 120;   %每组200个样本
% 创建索引
% 创建一个1x720的向量
i = 1:size(input);
% 创建一个和i同样大小的逻辑向量，所有元素默认为false
train_idx = false(size(i));
% 每120个元素中选择前84个作为训练集
for block = 1:numClasses
    start_idx = (block-1)*jg + 1;
    end_idx = block*jg;
    train_idx(start_idx:start_idx+round(0.7*jg)-1) = true;
end
% 获取训练集索引
training_set_idx = i(train_idx);
% 打印训练集索引
disp(training_set_idx);
% 创建一个和i同样大小的逻辑向量，所有元素默认为false
test_idx = false(size(i));
% 因为所有的索引要么是训练集要么是测试集，我们可以通过反转训练集的逻辑索引得到测试集的逻辑索引
test_idx = ~train_idx;
% 获取测试集索引
testing_set_idx = i(test_idx);
% 打印测试集索引
disp(testing_set_idx);
%% 划分训练集和测试集
input_train=[input(training_set_idx)];
input_test=[input(testing_set_idx)];

for i = 1:size(input_train,1)
    trainD(:,:,:,i) = input_train{i};
end
%训练集标签 
train_Y = categorical(cell2mat(output(training_set_idx)));
%测试集
for i = 1:size(input_test,1)
    testD(:,:,:,i) = input_test{i};
end
%测试集标签
test_Y =  categorical(cell2mat(output(testing_set_idx)));


%% 调用se_resnet
load se_resnet18.mat
% 将优化目标参数传进来的值 转换为需要的超参数
NumNeurons =256;                     %% 神经元个数

net = se_resnet18;
% 获取除了最后三层之外的所有层
layersTransfer = net.Layers(2:end-3);
connections = net.Connections(2:83,:);
connections =[connections; net.Connections(85:end-2,:)];%选取resnet18的链接方式
layersTransfer = CreateLgraphUsingConnections(layersTransfer,connections);%按照resnet18的方式重新组合网络
% 添加图像输入层
tempLayers = [imageInputLayer([size(input{1},1) size(input{1},2) size(input{1},3)],'Name','input1')% 建立输入层，输入数据结构为[num_dim, 1, 1]
sequenceFoldingLayer("Name", "seqfold")]; % 建立序列折叠层
layersTransfer = addLayers(layersTransfer, tempLayers); % 将上述网络结构加入空白结构中
layersTransfer = connectLayers(layersTransfer, 'seqfold/out', 'conv1');
lgraph=layersTransfer;

maxPosition = 256;
numHeads = 4;
numKeyChannels = numHeads*3;
tempLayers = [
    sequenceUnfoldingLayer("Name", "sequnfold") % 建立序列反折叠层
    batchNormalizationLayer("Name", "batch")
    flattenLayer("Name","flatten_4")
    selfAttentionLayer(6,60,"Name","selfattention1000","AttentionMask","causal","DropoutProbability",0.001);
    gruLayer(NumNeurons,"Name","gru")];

lgraph = addLayers(lgraph, tempLayers); % 将上述网络结构加入空白结构中
lgraph = connectLayers(lgraph, 'pool5', 'sequnfold/in');
% lgraph = connectLayers(lgraph, "seqfold/out", "fc1"); % 折叠层输出 连接卷积层输入  conv_1
lgraph = connectLayers(lgraph, "seqfold/miniBatchSize", "sequnfold/miniBatchSize");
%%

SEblockName = '6a';
ecaLayers = SEBlock(256,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'batch', 'flatten_4');  %拆连接
lgraph = connectLayers(lgraph, 'batch', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'batch', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'flatten_4');

%新建网络输出层，原始的resnet网络输出层是1000个，这里要重新赋值成我们的类别个数
newFullyConnectLayer = fullyConnectedLayer(256,'name','fc1');
%将新的输出全连接层替换原先的全连接层
lgraph = replaceLayer(lgraph, "pool5",newFullyConnectLayer);

tempLayers = selfAttentionLayer(12,768,"Name","selfattention","AttentionMask","causal","DropoutProbability",0.001);
lgraph = addLayers(lgraph,tempLayers);
tempLayers = selfAttentionLayer(6,60,"Name","selfattention_1","AttentionMask","causal","DropoutProbability",0.001);
lgraph = addLayers(lgraph,tempLayers);
tempLayers = selfAttentionLayer(12,768,"Name","selfattention_2","AttentionMask","causal","DropoutProbability",0.001);
lgraph = addLayers(lgraph,tempLayers);
tempLayers = [
    additionLayer(3,"Name","addition")
    fullyConnectedLayer(numClasses, 'Name', 'fc_out')
    softmaxLayer("Name", "softmax") % softmax激活层
    classificationLayer("Name", "classification")];%分类层
lgraph = addLayers(lgraph,tempLayers);
lgraph = connectLayers(lgraph,"gru","selfattention");
lgraph = connectLayers(lgraph,"gru","selfattention_1");
lgraph = connectLayers(lgraph,"gru","selfattention_2");
lgraph = connectLayers(lgraph,"selfattention","addition/in1");
lgraph = connectLayers(lgraph,"selfattention_1","addition/in2");
lgraph = connectLayers(lgraph,"selfattention_2","addition/in3");

% analyzeNetwork(lgraph)
 %%  保存网络结构
net=lgraph;
save Working_condition1_trained_Transformer net
%% Set the hyper parameters for unet training
learning_rate = 0.001;                        %% 学习率
MaxEpochs=10;

options = trainingOptions('sgdm', ...                 % 优化算法Adamadam
    'MaxEpochs',MaxEpochs, ...                          % 最大训练次数
    'InitialLearnRate', learning_rate, ...         % 初始学习率
    'LearnRateSchedule','piecewise', ......  %动态调整学习率
    'LearnRateDropPeriod',round(0.8*MaxEpochs), ...
    'LearnRateDropFactor',0.00001, ...  %学习率下降因子为0.00001
    'MiniBatchSize',32,...
    'GradientThreshold', 1, ...                       % 梯度阈值
    'ExecutionEnvironment', 'auto',...                 % 训练环境
    'Verbose', 1, ...                                 % 关闭优化过程
    'Plots', 'none');                    % 画出曲线
% % start training
t0 = tic;  %开始计时
net = trainNetwork(trainD,train_Y, lgraph,options);
toc(t0); % 从t0开始到此处的执行时间 

%%  查看网络结构
pred = classify(net,testD);
accuracy=sum(test_Y==pred)/length(pred)
% 画方框图
figure('Position',[100 50 800 600])
confMat = confusionmat(test_Y,pred);  %test_Y是真实值标签
zjyanseplotConfMat(confMat.');
xlabel('Predicted label')
ylabel('Real label')
title(['SE-Resnet测试集正确率 = ',num2str(accuracy*100),' %'])

%% 原始样本分布
layerT = 'input1';
LayersTNeed = activations(net,trainD,layerT,'OutputAs','channels');% 支路1
RawFeature = [];
for i = 1:size(LayersTNeed,4)
    temp = reshape(LayersTNeed(:,:,:,i),1,[]);
    RawFeature = [RawFeature;temp];
end
tsne_data = tsne(RawFeature);

temp = [];
NumTypes = 10;  %故障类别数
for i = 1:size(train_Y,1)
headers = {'故障类别1';'故障类别2';'故障类别3';'故障类别4';'故障类别5';'故障类别6'};
cmap = hsv(NumTypes);
lable = double(train_Y);
% 计算训练集每类（第一类为例）多少种故障
str = headers(lable(i));
tempdata = categorical(cellstr(str));
temp = [temp;tempdata];
end
% 类别标签
species = temp;
% 二维图像
figure('Position',[100,50,1200,500])
subplot(1,2,1)
gscatter(tsne_data(:,1),tsne_data(:,2),species,cmap,'.',20,'on');
% 添加整张图的主标题
title('原始样本分布', 'Interpreter', 'none', 'FontSize', 14);
hold on 
%% 模型识别后样本分布
layerT = 'fc_out';
LayersTNeed = activations(net,trainD,layerT,'OutputAs','channels');% 支路1
LayersTNeed=LayersTNeed';
RawFeature = [];
for i = 1:size(LayersTNeed,1)
    temp = reshape(LayersTNeed(i,:),1,[]);
    RawFeature = [RawFeature;temp];
end
tsne_data = tsne(RawFeature);
temp = [];
NumTypes = 10;  %故障类别数
for i = 1:size(train_Y,1)
headers = {'故障类别1';'故障类别2';'故障类别3';'故障类别4';'故障类别5';'故障类别6'};
cmap = hsv(NumTypes);
lable = double(train_Y);
% 计算训练集每类（第一类为例）多少种故障
str = headers(lable(i));
tempdata = categorical(cellstr(str));
temp = [temp;tempdata];
end
% 类别标签
species = temp;
% 二维图像
subplot(1,2,2)
gscatter(tsne_data(:,1),tsne_data(:,2),species,cmap,'.',20,'on');
% 添加整张图的主标题
title('改进模型识别后样本分布', 'Interpreter', 'none', 'FontSize', 14);
hold off


%% 提取fc_out特征
layer = 'fc_out';
p_train = activations(net,trainD,layer,'OutputAs','rows');
p_test  = activations(net,testD, layer,'OutputAs','rows');
M = size(p_train, 1);
N = size(p_test , 1);
%%  类型转换
p_train =  double(p_train); p_test  =  double(p_test);
t_train =  double(train_Y); t_test  =  double(test_Y);

%% 创建/训练SVM模型
cmd = [' -t 2',' -c ',num2str(100),' -g ',num2str(0.01)];
model = svmtrain(t_train,p_train,cmd);
%% SVM仿真测试
T_sim1 = svmpredict(t_train,p_train,model);
T_sim2 = svmpredict(t_test,p_test,model);

%% 性能评价
error1 = sum((T_sim1 == t_train))/M * 100 ;
error2 = sum((T_sim2 == t_test))/N * 100 ;
%%  绘图
figure
plot(1:M,t_train,'c*',1:M,T_sim1,'mo','LineWidth',1)
legend('真实值','预测值')
xlabel('预测样本')
ylabel('预测结果')
string={'训练集预测结果对比';['准确率=' num2str(error1) '%']};
title(string)
grid

figure
plot(1:N,t_test,'c*',1:N,T_sim2,'mo','LineWidth',1)
legend('真实值','预测值')
xlabel('预测样本')
ylabel('预测结果')
string={'测试集预测结果对比';['准确率=' num2str(error2) '%']};
title(string)
grid
