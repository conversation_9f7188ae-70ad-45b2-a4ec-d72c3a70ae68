"""
使用外部数据集测试故障诊断系统
验证数据增强对模型性能的改善效果
"""

from fault_diagnosis_system import FaultDiagnosisSystem
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import numpy as np
from sklearn.model_selection import train_test_split

def test_with_external_data():
    """使用外部数据集测试"""
    print("=" * 70)
    print("使用外部数据集的故障诊断系统测试")
    print("=" * 70)
    
    # 初始化系统
    system = FaultDiagnosisSystem()
    
    try:
        # 1. 生成合并数据集
        print("\n1. 数据生成（真实数据 + 外部数据集）")
        print("-" * 50)
        
        X, y, fault_types = system.generate_sample_data(
            num_classes=13, 
            samples_per_class=100,  # 增加样本数量
            signal_length=1024,     # 适中的信号长度
            use_external=True       # 使用外部数据集
        )
        
        print(f"总数据集大小: {X.shape}")
        print(f"故障类型数量: {len(fault_types)}")
        
        # 分析数据分布
        print("\n数据分布分析:")
        unique_labels, counts = np.unique(y, return_counts=True)
        for label, count in zip(unique_labels, counts):
            if label < len(fault_types):
                print(f"  {fault_types[label]}: {count} 样本")
        
        # 2. 数据分割
        print("\n2. 数据分割")
        print("-" * 50)
        
        X_train, X_temp, y_train, y_temp = train_test_split(
            X, y, test_size=0.4, random_state=42, stratify=y
        )
        X_val, X_test, y_val, y_test = train_test_split(
            X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp
        )
        
        print(f"训练集: {X_train.shape[0]} 样本")
        print(f"验证集: {X_val.shape[0]} 样本") 
        print(f"测试集: {X_test.shape[0]} 样本")
        
        # 3. 模型训练
        print("\n3. 模型训练")
        print("-" * 50)
        
        training_history = system.train_model(
            X_train, y_train, X_val, y_val,
            epochs=50,      # 增加训练轮数
            batch_size=16   # 适中的批次大小
        )
        
        # 4. 模型评估
        print("\n4. 模型评估")
        print("-" * 50)
        
        evaluation_results = system.evaluate_model(X_test, y_test, fault_types)
        
        # 5. 结果分析
        print("\n5. 性能分析")
        print("-" * 50)
        
        final_test_acc = evaluation_results['accuracy']
        final_train_acc = training_history['train_accs'][-1]
        final_val_acc = training_history['val_accs'][-1]
        
        print(f"✓ 最终测试准确率: {final_test_acc:.2f}%")
        print(f"✓ 最终训练准确率: {final_train_acc:.2f}%")
        print(f"✓ 最终验证准确率: {final_val_acc:.2f}%")
        
        # 过拟合检查
        overfitting = final_train_acc - final_test_acc
        print(f"✓ 过拟合程度: {overfitting:.2f}%")
        
        if overfitting > 20:
            print("⚠️ 检测到严重过拟合")
        elif overfitting > 10:
            print("⚠️ 检测到轻微过拟合")
        else:
            print("✓ 模型泛化良好")
        
        # 性能评级
        if final_test_acc > 70:
            grade = "优秀"
            emoji = "🎉"
        elif final_test_acc > 50:
            grade = "良好"
            emoji = "👍"
        elif final_test_acc > 30:
            grade = "一般"
            emoji = "📈"
        else:
            grade = "需要改进"
            emoji = "⚠️"
            
        print(f"\n{emoji} 模型性能评级: {grade}")
        
        # 6. 改进建议
        print("\n6. 改进建议")
        print("-" * 50)
        
        if final_test_acc < 30:
            print("建议采取以下措施:")
            print("• 增加更多高质量的训练数据")
            print("• 优化特征提取方法（VMD参数、SDP转换）")
            print("• 调整模型架构（层数、神经元数量）")
            print("• 尝试不同的优化器和学习率")
            print("• 增强数据预处理和清理")
        elif final_test_acc < 50:
            print("进一步优化建议:")
            print("• 调整超参数（学习率、dropout、权重衰减）")
            print("• 尝试不同的数据增强技术")
            print("• 考虑使用预训练模型或迁移学习")
            print("• 优化损失函数和评估指标")
        elif final_test_acc < 70:
            print("性能提升建议:")
            print("• 精细调整超参数")
            print("• 尝试集成学习方法")
            print("• 增加模型复杂度或使用更先进的架构")
            print("• 收集更多边界案例数据")
        else:
            print("模型表现优秀！可以考虑:")
            print("• 部署到生产环境")
            print("• 进行实时性能测试")
            print("• 收集实际应用反馈")
            print("• 持续监控和改进")
        
        return {
            'test_accuracy': final_test_acc,
            'train_accuracy': final_train_acc,
            'val_accuracy': final_val_acc,
            'overfitting': overfitting,
            'grade': grade,
            'data_size': X.shape[0],
            'num_classes': len(fault_types)
        }
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_with_without_external():
    """比较使用和不使用外部数据集的效果"""
    print("\n" + "=" * 70)
    print("对比测试：使用 vs 不使用外部数据集")
    print("=" * 70)
    
    system = FaultDiagnosisSystem()
    
    results = {}
    
    for use_external in [False, True]:
        data_type = "外部数据集" if use_external else "仅真实数据"
        print(f"\n测试配置: {data_type}")
        print("-" * 40)
        
        try:
            # 生成数据
            X, y, fault_types = system.generate_sample_data(
                samples_per_class=50,
                signal_length=1024,
                use_external=use_external
            )
            
            # 数据分割
            X_train, X_temp, y_train, y_temp = train_test_split(
                X, y, test_size=0.4, random_state=42, stratify=y
            )
            X_val, X_test, y_val, y_test = train_test_split(
                X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp
            )
            
            # 训练模型
            training_history = system.train_model(
                X_train, y_train, X_val, y_val,
                epochs=30, batch_size=16
            )
            
            # 评估模型
            evaluation_results = system.evaluate_model(X_test, y_test, fault_types)
            
            results[data_type] = {
                'test_acc': evaluation_results['accuracy'],
                'train_acc': training_history['train_accs'][-1],
                'data_size': X.shape[0],
                'num_classes': len(fault_types)
            }
            
            print(f"结果: 测试准确率 {evaluation_results['accuracy']:.2f}%")
            
        except Exception as e:
            print(f"测试失败: {e}")
            results[data_type] = None
    
    # 比较结果
    print("\n" + "=" * 70)
    print("对比结果总结")
    print("=" * 70)
    
    if all(results.values()):
        real_only = results["仅真实数据"]
        with_external = results["外部数据集"]
        
        improvement = with_external['test_acc'] - real_only['test_acc']
        
        print(f"仅真实数据:     测试准确率 {real_only['test_acc']:.2f}%")
        print(f"使用外部数据集: 测试准确率 {with_external['test_acc']:.2f}%")
        print(f"性能提升:       {improvement:+.2f}%")
        
        if improvement > 5:
            print("🎉 外部数据集显著提升了模型性能！")
        elif improvement > 0:
            print("👍 外部数据集有助于提升模型性能")
        else:
            print("⚠️ 外部数据集未能提升性能，可能需要优化数据质量")
    
    return results

def main():
    """主测试函数"""
    # 1. 使用外部数据集测试
    results1 = test_with_external_data()
    
    # 2. 对比测试
    results2 = compare_with_without_external()
    
    print("\n" + "=" * 70)
    print("测试完成！")
    print("=" * 70)
    
    return results1, results2

if __name__ == "__main__":
    main()
