function [polarRadius, clockwiseDeflection, counterclockwiseDeflection, colorFactor] = colorSDPAlgorithm(residualSignal, epsilon,l)
    
    % 获取残差信号的数量
    m = length(residualSignal)-100;
    % 计算残差信号的最小值和最大值
    resMin = min(residualSignal);
    resMax = max(residualSignal);
    % 初始化输出数组
    polarRadius = zeros(m, 1);
    clockwiseDeflection = zeros(m, 1);
    counterclockwiseDeflection = zeros(m, 1);
    colorFactor = zeros(m, 1);    
    % polarRadius=[];
    % clockwiseDeflection=[];
    % counterclockwiseDeflection=[];
    % colorFactor=[];
    % 设置镜像对称旋转角度和偏转角度增益
    theta = pi/2;
    eta = -30 * (pi / 180); % 将角度转换为弧度 
    for i = 1:m
        % 计算极径r(m)，根据公式(17)
        polarRadius(i) = (residualSignal(i) - resMin) / (resMax - resMin);
        % 计算顺时针偏转角度theta(m)，根据公式(18)
        if i + l <= m
            clockwiseDeflection(i) = theta + ((residualSignal(i + l) - resMin) / (resMax - resMin)) * eta;
            counterclockwiseDeflection(i) = theta - ((residualSignal(i + l) - resMin) / (resMax - resMin)) * eta;
        else
            % 处理边界情况，当i + l超出信号长度时，采用循环索引
            validIndex = mod(i + l - 1, m) + 1; % 使用mod函数确保索引在1到m之间循环
            clockwiseDeflection(i) = theta + ((residualSignal(validIndex) - resMin) / (resMax - resMin)) * eta;
            counterclockwiseDeflection(i) = theta - ((residualSignal(validIndex) - resMin) / (resMax - resMin)) * eta;
        end
        polarRadius=[polarRadius; polarRadius(i)];
        clockwiseDeflection=[clockwiseDeflection; clockwiseDeflection(i)];
        counterclockwiseDeflection=[counterclockwiseDeflection; counterclockwiseDeflection(i)];
        % 计算颜色参数c(m)，即邻域的标准差，根据公式(21)和(22)
        neighborhood = [];
        for j = max(1, i - epsilon):max(i + epsilon, m-1)
            neighborhood = [neighborhood , residualSignal(j)];
        end
        % disp(['Neighborhood for point ', num2str(i), ': ', num2str(neighborhood)]); % 打印邻域数据
        colorFactor(i) = std(neighborhood);
    end 


% % 获取残差信号的长度
%     m = length(residualSignal);
% 
%     % 计算残差信号的最小值和最大值
%     resMin = min(residualSignal);
%     resMax = max(residualSignal);
% 
%     % 初始化输出数组
%     polarRadius = zeros(m, 1);
%     clockwiseDeflection = zeros(m, 1);
%     counterclockwiseDeflection = zeros(m, 1);
%     colorFactor = zeros(m, 1);
% 
%     % 设置镜像对称旋转角度和偏转角度增益
%     theta = pi / 3;
%     eta = 60 * (pi / 180); % 将角度转换为弧度
% 
%     % 计算滞后因子l（根据文章中的建议，这里设置为1500，实际可根据信号特性调整）
%     l = 1500; 
% 
%     for i = 1:m
%         % 计算极径r(m)，根据公式(17)
%         polarRadius(i) = (residualSignal(i) - resMin) / (resMax - resMin);
% 
%         % 计算顺时针偏转角度theta(m)，根据公式(18)
%         if i + l <= m
%             clockwiseDeflection(i) = theta + ((residualSignal(i + l) + resMin) / (resMax - resMin)) * eta;
%             counterclockwiseDeflection(i) = theta - ((residualSignal(i + l) + resMin) / (resMax - resMin)) * eta;
%         else
%             % 处理边界情况，当i + l超出信号长度时，采用循环索引
%             validIndex = mod(i + l - 1, m) + 1; % 使用mod函数确保索引在1到m之间循环
%             clockwiseDeflection(i) = theta + ((residualSignal(validIndex) + resMin) / (resMax - resMin)) * eta;
%             counterclockwiseDeflection(i) = theta - ((residualSignal(validIndex) + resMin) / (resMax - resMin)) * eta;
%         end
% 
%         % 计算颜色参数c(m)，即邻域的标准差，根据公式(21)和(22)
%         neighborhood = [];
%         for j = max(1, i - epsilon):min(i + epsilon, m)
%             neighborhood = [neighborhood, residualSignal(j)];
%         end
%         % disp(['Neighborhood for point ', num2str(i), ': ', num2str(neighborhood)]); % 打印邻域数据
%         colorFactor(i) = std(neighborhood);
%     end
% end