function iCali = currentSignalCalibration(iTrRaw11, iRaw, fs)
% 电流信号校准算法
N = length(iRaw);
Rij = zeros(1, 2 * N - 1);
for t = -N + 1: N - 1
    if t >= 0
        Rij(t + N) = sum(iTrRaw11(t * fs + 1: N).* iRaw(1: N - t * fs));
    else
        Rij(t + N) = sum(iTrRaw11(1: N + t * fs).* iRaw(-t * fs + 1: N));
    end
end
[~, t_m] = max(Rij);
t_m = t_m - N;
M = fs * t_m;

if t_m < 0
    % 确保 iRaw 向右移动 M 个数据点后与 iTrRaw11 长度匹配
    iRaw_extended = [iRaw; zeros(M, 1)]; % 在 iRaw 末尾补零，使其长度增加 M
    iCali = [iRaw_extended(-t_m + 1: end); iTrRaw11(1: length(iTrRaw11) - length(iRaw_extended(-t_m + 1: end)))];
else
    % 确保 iRaw 向左移动 M 个数据点后与 iTrRaw11 长度匹配
    iRaw_extended = [zeros(M, 1); iRaw]; % 在 iRaw 开头补零，使其长度增加 M
    iCali = [iRaw_extended(M + 1: end); iRaw_extended(1: M)];
end
end