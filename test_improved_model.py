"""
测试改进后的故障诊断模型
解决低准确率问题的验证脚本
"""

from fault_diagnosis_system import FaultDiagnosisSystem
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import numpy as np
from sklearn.model_selection import train_test_split

def main():
    print("=" * 60)
    print("测试改进后的六相永磁同步电机故障诊断系统")
    print("=" * 60)
    
    # 初始化系统
    system = FaultDiagnosisSystem()
    
    # 1. 生成数据并分析
    print("\n1. 数据生成和分析")
    print("-" * 40)
    
    try:
        X, y, fault_types = system.generate_sample_data(
            num_classes=13, samples_per_class=30, signal_length=2048
        )
        print(f"数据生成成功: {X.shape}")
        
        # 分析数据质量
        print("\n数据质量分析:")
        for class_idx in range(13):
            class_mask = y == class_idx
            class_data = X[class_mask]
            
            if len(class_data) > 0:
                class_std = np.std(class_data)
                class_range = [np.min(class_data), np.max(class_data)]
                
                status = "正常" if class_std > 1e-6 else "异常(常数)"
                print(f"{fault_types[class_idx]:15s}: 标准差={class_std:8.4f}, 范围=[{class_range[0]:7.3f}, {class_range[1]:7.3f}] - {status}")
        
        # 2. 数据分割
        print("\n2. 数据分割")
        print("-" * 40)
        
        X_train, X_temp, y_train, y_temp = train_test_split(
            X, y, test_size=0.4, random_state=42, stratify=y
        )
        X_val, X_test, y_val, y_test = train_test_split(
            X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp
        )
        
        print(f"训练集: {X_train.shape[0]} 样本")
        print(f"验证集: {X_val.shape[0]} 样本") 
        print(f"测试集: {X_test.shape[0]} 样本")
        
        # 3. 模型训练（使用改进的参数）
        print("\n3. 模型训练")
        print("-" * 40)
        
        training_history = system.train_model(
            X_train, y_train, X_val, y_val,
            epochs=100,  # 增加训练轮数
            batch_size=16  # 减小批次大小
        )
        
        # 4. 模型评估
        print("\n4. 模型评估")
        print("-" * 40)
        
        evaluation_results = system.evaluate_model(X_test, y_test, fault_types)
        
        # 5. 结果总结
        print("\n5. 结果总结")
        print("-" * 40)
        print(f"✓ 最终测试准确率: {evaluation_results['accuracy']:.2f}%")
        print(f"✓ 最终训练准确率: {training_history['train_accs'][-1]:.2f}%")
        print(f"✓ 最终验证准确率: {training_history['val_accs'][-1]:.2f}%")
        
        # 分析改进效果
        if evaluation_results['accuracy'] > 50:
            print("🎉 模型性能显著改善！")
        elif evaluation_results['accuracy'] > 20:
            print("📈 模型性能有所改善，但仍需优化")
        else:
            print("⚠️ 模型性能仍需进一步改进")
            
        # 6. 问题诊断
        print("\n6. 问题诊断建议")
        print("-" * 40)
        
        if evaluation_results['accuracy'] < 30:
            print("可能的问题和解决方案:")
            print("1. 数据质量问题 - 检查是否有常数信号或异常值")
            print("2. 特征提取问题 - 优化VMD参数或SDP转换")
            print("3. 模型复杂度 - 调整网络结构或超参数")
            print("4. 训练策略 - 增加训练轮数或调整学习率")
            
        return evaluation_results
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = main()
