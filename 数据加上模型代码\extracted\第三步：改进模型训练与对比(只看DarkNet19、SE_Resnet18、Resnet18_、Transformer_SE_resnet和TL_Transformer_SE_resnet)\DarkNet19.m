%%  清空环境变量 来自公众号：《淘个代码》
warning off             % 关闭报警信息
close all               % 关闭开启的图窗
clear                   % 清空变量
clc  
addpath(genpath(pwd))

load Resize_Working_condition1.mat %加载数据集
resizeimg = Resize_Working_condition1';
input=resizeimg(:,1);
output=resizeimg(:,2);
numClasses = length(unique(cell2mat(output))); %分类个数
jg = 120;   %每组200个样本
% 创建索引
% 创建一个1x720的向量
i = 1:size(input);
% 创建一个和i同样大小的逻辑向量，所有元素默认为false
train_idx = false(size(i));
% 每120个元素中选择前84个作为训练集
for block = 1:numClasses
    start_idx = (block-1)*jg + 1;
    end_idx = block*jg;
    train_idx(start_idx:start_idx+round(0.7*jg)-1) = true;
end
% 获取训练集索引
training_set_idx = i(train_idx);
% 打印训练集索引
disp(training_set_idx);
% 创建一个和i同样大小的逻辑向量，所有元素默认为false
test_idx = false(size(i));
% 因为所有的索引要么是训练集要么是测试集，我们可以通过反转训练集的逻辑索引得到测试集的逻辑索引
test_idx = ~train_idx;
% 获取测试集索引
testing_set_idx = i(test_idx);
% 打印测试集索引
disp(testing_set_idx);
%% 划分训练集和测试集
input_train=[input(training_set_idx)];
input_test=[input(testing_set_idx)];

for i = 1:size(input_train,1)
    trainD(:,:,:,i) = input_train{i};
end
%训练集标签 
train_Y = categorical(cell2mat(output(training_set_idx)));
%测试集
for i = 1:size(input_test,1)
    testD(:,:,:,i) = input_test{i};
end
%测试集标签
test_Y =  categorical(cell2mat(output(testing_set_idx)));


%% 利用寻优得到参数重新训练与预测 
%% 调用DarkNet
load DarkNet19.mat
% 将优化目标参数传进来的值 转换为需要的超参数
learning_rate = 0.00001;                        %% 学习率
NumNeurons =128;                     %% 神经元个数
net = DarkNet19;
% 获取除了最后三层之外的所有层
layersTransfer = net.Layers(2:end-3);
% 创建一个新的层图
lgraph = layerGraph();
% 添加图像输入层
lgraph = addLayers(lgraph,  imageInputLayer([size(input{1},1) size(input{1},2) size(input{1},3)],'Name','input1'));
% DarkNet后3层接上GRU-Attention组成DarkNet-GRU-Attention
tempLayers = [
    layersTransfer
    flattenLayer("Name","flatten")
    fullyConnectedLayer(numClasses, 'Name', 'fc_new') % 新的全连接层
    softmaxLayer
    classificationLayer];
% 连接倒数第四层到新的flatten层
lgraph = addLayers(lgraph, tempLayers); % 将上述网络结构加入空白结构中
lgraph = connectLayers(lgraph, 'input1', 'conv1');

%新建网络输出层，原始的resnet网络输出层是1000个，这里要重新赋值成我们的类别个数
newFullyConnectLayer = fullyConnectedLayer(numClasses,'name','fc_out');
%将新的输出全连接层替换原先的全连接层
lgraph = replaceLayer(lgraph, "fc_new",newFullyConnectLayer);
%将新的classificationLayer层替换原先的classificationLayer层

%% Set the hyper parameters for unet training
options = trainingOptions('adam', ...                 % 优化算法Adam
    'MaxEpochs', 10, ...                            % 最大训练次数
    'LearnRateSchedule','piecewise', ......  %动态调整学习率
    'LearnRateDropFactor',learning_rate , ...  %学习率下降因子为0.00001
    'MiniBatchSize',32,...
    'GradientThreshold', 1, ...                       % 梯度阈值
    'InitialLearnRate', 0.001, ...         % 初始学习率
    'ExecutionEnvironment', 'auto',...                 % 训练环境
    'Verbose', 1, ...                                 % 关闭优化过程
    'Plots', 'none');                    % 画出曲线
% % start training
t0 = tic;  %开始计时
net = trainNetwork(trainD,train_Y, lgraph,options);
toc(t0); % 从t0开始到此处的执行时间 


%%  查看网络结构
pred = classify(net,testD);
accuracy1=sum(test_Y==pred)/length(pred);

% 画方框图
figure('Position',[100 50 800 600])
confMat = confusionmat(test_Y,pred);  %test_Y是真实值标签
zjyanseplotConfMat(confMat.');
xlabel('Predicted label')
ylabel('Real label')
title(['DarkNet19测试集正确率 = ',num2str(accuracy1*100),' %'])

%% 原始样本分布
layerT = 'input1';
LayersTNeed = activations(net,trainD,layerT,'OutputAs','channels');% 支路1
RawFeature = [];
for i = 1:size(LayersTNeed,4)
    temp = reshape(LayersTNeed(:,:,:,i),1,[]);
    RawFeature = [RawFeature;temp];
end
tsne_data = tsne(RawFeature);
temp = [];
NumTypes = 10;  %故障类别数
for i = 1:size(train_Y,1)
headers = {'故障类别1';'故障类别2';'故障类别3';'故障类别4';'故障类别5';'故障类别6'};
cmap = hsv(NumTypes);
lable = double(train_Y);
% 计算训练集每类（第一类为例）多少种故障
str = headers(lable(i));
tempdata = categorical(cellstr(str));
temp = [temp;tempdata];
end
% 类别标签
species = temp;
% 二维图像
figure('Position',[100,50,1200,500])
subplot(1,2,1)
gscatter(tsne_data(:,1),tsne_data(:,2),species,cmap,'.',20,'on');
% 添加整张图的主标题
title('原始样本分布', 'Interpreter', 'none', 'FontSize', 14);
hold on 
%% 模型识别后样本分布
layerT = 'fc_out';
LayersTNeed = activations(net,trainD,layerT,'OutputAs','channels');% 支路1
LayersTNeed=LayersTNeed';
RawFeature = [];
for i = 1:size(LayersTNeed,1)
    temp = reshape(LayersTNeed(i,:),1,[]);
    RawFeature = [RawFeature;temp];
end
tsne_data = tsne(RawFeature);
temp = [];
NumTypes = 10;  %故障类别数
for i = 1:size(train_Y,1)
headers = {'故障类别1';'故障类别2';'故障类别3';'故障类别4';'故障类别5';'故障类别6'};
cmap = hsv(NumTypes);
lable = double(train_Y);
% 计算训练集每类（第一类为例）多少种故障
str = headers(lable(i));
tempdata = categorical(cellstr(str));
temp = [temp;tempdata];
end
% 类别标签
species = temp;
% 二维图像
subplot(1,2,2)
gscatter(tsne_data(:,1),tsne_data(:,2),species,cmap,'.',20,'on');
% 添加整张图的主标题
title('DarkNet19模型识别后样本分布', 'Interpreter', 'none', 'FontSize', 14);
hold off


