function layers = SEBlock(C, r,num)
% C is the number of channels in the previous layer
% r is the reduction ratio for the SE block
    reducedDims = max(floor(C / r), 1); % Ensure at least one dimension
    layers = [
        globalAveragePooling2dLayer( 'Name', ['se_global_pool_',num])
        % averagePooling2dLayer(1, 'Stride', 1, 'Name', ['se_avg_pool_',num], 'Padding', 'same')
        fullyConnectedLayer(reducedDims, 'Name', ['se_fc1_',num])
        reluLayer('Name', ['se_relu_',num])
        fullyConnectedLayer(C, 'Name', ['se_fc2_',num])
        sigmoidLayer('Name', ['se_sigmoid_',num])
    ];
end