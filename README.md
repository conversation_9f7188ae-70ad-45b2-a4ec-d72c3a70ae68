# 六相永磁同步电机故障诊断系统

基于自适应深度迁移学习的完整故障诊断系统实现，包含数据增强、VMD+CPO分解、颜色-SDP转换和SSE-Transformer模型。

## 🚀 主要功能

### 1. 数据增强模块
- **互相关信号校准**：自动校准不同传感器的信号时间偏移
- **比例影响因素扩展**：通过比例系数扩展训练样本
- **附加影响因素扩展**：添加高斯噪声增强数据多样性
- **3D可视化**：直观展示数据增强效果

### 2. VMD+CPO分解模块
- **CPO优化器**：冠毛豪猪优化算法自动优化VMD参数
- **变分模态分解**：将信号分解为多个本征模态函数(IMF)
- **参数自适应**：自动确定最优的alpha和K参数
- **收敛曲线可视化**：实时监控优化过程

### 3. SDP特征提取模块
- **对称点模式**：将IMF分量转换为极坐标特征
- **颜色映射SDP**：使用颜色区分不同IMF分量
- **普通SDP**：传统的对称点模式表示
- **特征图像生成**：将SDP转换为深度学习可用的图像特征

### 4. SSE-Transformer模型
- **SE注意力机制**：增强重要特征的表达能力
- **Transformer架构**：捕获长距离依赖关系
- **多头自注意力**：并行处理不同类型的特征
- **端到端训练**：直接从SDP图像到故障分类

## 📊 支持的故障类型

系统可以诊断13种不同的故障类型：

1. **Normal** - 正常状态
2. **REVD** - 转子励磁电压断开
3. **OP** - 缺相故障
4. **VREC** - 转子励磁电流变化
5. **2PSC** - 两相短路
6. **1PSC** - 单相对中性点短路
7. **B_Open** - B相断开
8. **W_Open** - W相断开
9. **T3_Open** - T3开路
10. **T11_Open** - T11开路
11. **T3T5_Open** - T3和T5开路
12. **Current_Sensor_1** - 电流传感器1故障
13. **Current_Sensor_2** - 电流传感器2故障

## 🛠️ 安装和使用

### 环境要求
- Python 3.8+
- PyTorch 1.12+
- CUDA (可选，用于GPU加速)

### 安装依赖
```bash
pip install -r requirements.txt
```

### 快速开始

#### 1. 运行完整演示
```python
python run_demo.py
```

#### 2. 使用主系统
```python
from fault_diagnosis_system import FaultDiagnosisSystem

# 初始化系统
system = FaultDiagnosisSystem()

# 生成示例数据
X, y, fault_types = system.generate_sample_data(
    num_classes=13, 
    samples_per_class=100, 
    signal_length=2048
)

# 处理单个信号
signal = X[0]
results = system.process_signal_pipeline(signal, plot_results=True)

# 训练模型
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2)
X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.2)

training_history = system.train_model(
    X_train, y_train, X_val, y_val, 
    epochs=50, batch_size=32
)

# 评估模型
evaluation_results = system.evaluate_model(X_test, y_test, fault_types)
```

#### 3. 独立使用各个组件

**CPO优化器**
```python
from fault_diagnosis_system import CPOOptimizer

def objective_function(x):
    return np.sum(x**2)  # 示例目标函数

cpo = CPOOptimizer(pop_size=10, max_iter=50)
best_fitness, best_solution, conv_curve = cpo.optimize(objective_function)
```

**VMD分解**
```python
from fault_diagnosis_system import VMDDecomposer

vmd = VMDDecomposer(alpha=2000, K=4)
u, u_hat, omega = vmd.vmd(signal)
```

**SDP处理**
```python
from fault_diagnosis_system import SDPProcessor

sdp_processor = SDPProcessor()
sdp_data, colors = sdp_processor.compute_sdp(imf_components, use_color=True)
sdp_processor.plot_sdp(sdp_data, colors, title="SDP特征图")
```

## 📈 效果图展示

系统会自动生成以下类型的效果图：

1. **数据增强效果图**
   - 原始种子信号
   - 增强后的信号
   - 3D可视化
   - 统计特性对比
   - 互相关分析
   - 频域特性对比

2. **VMD分解效果图**
   - CPO优化收敛曲线
   - 原始信号
   - IMF分量分解结果
   - 频谱分析

3. **SDP转换效果图**
   - 颜色SDP极坐标图
   - 普通SDP极坐标图
   - 特征分布可视化

4. **模型性能效果图**
   - 训练和验证损失曲线
   - 训练和验证准确率曲线
   - 混淆矩阵热力图
   - t-SNE特征可视化
   - 分类报告

## 🔧 自定义配置

### 修改VMD参数
```python
vmd_decomposer = VMDDecomposer(
    alpha=2000,    # 带宽约束参数
    tau=0,         # 噪声容限
    K=4,           # 模态数
    DC=0,          # 直流分量
    init=1,        # 初始化方式
    tol=1e-7       # 收敛容限
)
```

### 修改CPO参数
```python
cpo_optimizer = CPOOptimizer(
    pop_size=10,      # 种群大小
    max_iter=50,      # 最大迭代次数
    lb=[500, 2],      # 下边界
    ub=[2500, 12],    # 上边界
    dim=2             # 维度
)
```

### 修改模型参数
```python
model = SSETransformer(
    input_size=4096,     # 输入特征维度
    num_classes=13,      # 分类数
    d_model=512,         # 模型维度
    nhead=8,             # 注意力头数
    num_layers=6,        # Transformer层数
    dim_feedforward=2048, # 前馈网络维度
    dropout=0.1          # Dropout率
)
```

## 📝 技术特点

- **自适应参数优化**：使用CPO算法自动优化VMD参数
- **多模态特征融合**：结合时域、频域和极坐标特征
- **注意力机制增强**：SE模块和Transformer提升特征表达
- **端到端学习**：从原始信号到故障分类的完整流水线
- **可视化丰富**：提供全面的分析和诊断可视化
- **模块化设计**：各组件可独立使用和扩展

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过Issue联系。
