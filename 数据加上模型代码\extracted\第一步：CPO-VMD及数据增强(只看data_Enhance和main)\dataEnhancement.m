function extendedSignals = dataEnhancement(seed_signals, nP, nA, ls, muBeta, sigmaBeta)
    % 获取seed_signals的维度信息
    [p, q] = size(seed_signals);
    
    % 初始化扩展后的训练数据集
    extendedSignals = zeros(p, (1 + nP + nA) * q);
    
    for i = 1:p
        for j = 1:q
            % 提取当前种子电流信号
            currentSeedSignal = seed_signals(i, j);
            
            % 基于比例影响因素扩展
            proportionalExtendedSignals = zeros(1, nP);
            for u = 1:nP
                if u <= nP / 2
                    alpha = 1 - u * ls;
                else
                    alpha = 1 + (u - nP / 2) * ls;
                end
                proportionalExtendedSignals(u) = currentSeedSignal * alpha;
            end
            
            % 基于附加影响因素扩展
            additionalExtendedSignals = zeros(1, nA);
            for v = 1:nA
                beta = muBeta + sigmaBeta * randn(length(currentSeedSignal), 1); % 生成随机附加系数序列
                additionalExtendedSignals(v) = currentSeedSignal + beta;
            end
            
            % 组合原始信号、比例扩展信号和附加扩展信号
            extendedSignals(i, (j - 1) * (1 + nP + nA) + 1 : j * (1 + nP + nA)) = [currentSeedSignal, proportionalExtendedSignals, additionalExtendedSignals];
        end
    end
end