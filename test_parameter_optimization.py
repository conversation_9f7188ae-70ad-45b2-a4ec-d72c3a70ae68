"""
测试参数优化功能
验证VMD参数优化是否正常工作
"""

from fault_diagnosis_system import FaultDiagnosisSystem
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import numpy as np
import matplotlib.pyplot as plt

def test_parameter_optimization():
    """测试参数优化功能"""
    print("=" * 60)
    print("参数优化功能测试")
    print("=" * 60)
    
    # 初始化系统
    system = FaultDiagnosisSystem()
    
    # 生成测试信号
    print("\n1. 生成测试信号")
    print("-" * 40)
    
    # 创建一个包含多个频率成分的复合信号
    t = np.linspace(0, 2, 1024)
    signal = (0.5 * np.sin(2*np.pi*10*t) +     # 10Hz成分
              0.3 * np.sin(2*np.pi*25*t) +     # 25Hz成分  
              0.2 * np.sin(2*np.pi*50*t) +     # 50Hz成分
              0.1 * np.random.randn(1024))     # 噪声
    
    print(f"测试信号长度: {len(signal)}")
    print("信号包含频率成分: 10Hz, 25Hz, 50Hz + 噪声")
    
    # 2. 测试VMD参数优化
    print("\n2. VMD参数优化测试")
    print("-" * 40)
    
    print("优化前默认参数:")
    print(f"  alpha = {system.vmd_decomposer.alpha}")
    print(f"  K = {system.vmd_decomposer.K}")
    
    # 执行参数优化
    try:
        best_params, conv_curve = system.optimize_vmd_parameters(signal)
        
        print("\n优化后参数:")
        print(f"  alpha = {system.vmd_decomposer.alpha}")
        print(f"  K = {system.vmd_decomposer.K}")
        
        print(f"\n优化结果:")
        print(f"  最优alpha: {int(best_params[0])}")
        print(f"  最优K: {int(best_params[1])}")
        print(f"  收敛曲线长度: {len(conv_curve)}")
        
        # 3. 比较优化前后的VMD分解效果
        print("\n3. 比较优化前后效果")
        print("-" * 40)
        
        # 使用默认参数分解
        from fault_diagnosis_system import VMDDecomposer
        default_vmd = VMDDecomposer(alpha=2000, K=4)
        u_default, _, _ = default_vmd.vmd(signal)
        
        # 使用优化参数分解
        u_optimized, _, _ = system.vmd_decomposer.vmd(signal)
        
        print("默认参数分解:")
        print(f"  模态数: {u_default.shape[0]}")
        print(f"  重构误差: {np.mean((signal - np.sum(u_default, axis=0))**2):.6f}")
        
        print("优化参数分解:")
        print(f"  模态数: {u_optimized.shape[0]}")
        print(f"  重构误差: {np.mean((signal - np.sum(u_optimized, axis=0))**2):.6f}")
        
        # 4. 绘制优化效果图
        print("\n4. 绘制优化效果图")
        print("-" * 40)
        
        # 绘制收敛曲线
        plt.figure(figsize=(12, 8))
        
        # 收敛曲线
        plt.subplot(2, 2, 1)
        plt.plot(conv_curve, 'b-', linewidth=2)
        plt.title('CPO优化收敛曲线')
        plt.xlabel('迭代次数')
        plt.ylabel('适应度值')
        plt.grid(True)
        
        # 原始信号
        plt.subplot(2, 2, 2)
        plt.plot(t, signal, 'k-', linewidth=1)
        plt.title('原始测试信号')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅值')
        plt.grid(True)
        
        # 默认参数分解结果
        plt.subplot(2, 2, 3)
        for i in range(min(4, u_default.shape[0])):
            plt.plot(t, u_default[i], label=f'IMF{i+1}', alpha=0.7)
        plt.title('默认参数VMD分解')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅值')
        plt.legend()
        plt.grid(True)
        
        # 优化参数分解结果
        plt.subplot(2, 2, 4)
        for i in range(min(4, u_optimized.shape[0])):
            plt.plot(t, u_optimized[i], label=f'IMF{i+1}', alpha=0.7)
        plt.title('优化参数VMD分解')
        plt.xlabel('时间 (s)')
        plt.ylabel('幅值')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('parameter_optimization_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("效果图已保存为: parameter_optimization_results.png")
        
        # 5. 评估优化效果
        print("\n5. 优化效果评估")
        print("-" * 40)
        
        # 计算评价指标
        default_orthogonality = system.calculate_orthogonality(u_default)
        optimized_orthogonality = system.calculate_orthogonality(u_optimized)
        
        default_freq_sep = system.calculate_frequency_separation(u_default)
        optimized_freq_sep = system.calculate_frequency_separation(u_optimized)
        
        default_recon_error = system.calculate_reconstruction_error(signal, u_default)
        optimized_recon_error = system.calculate_reconstruction_error(signal, u_optimized)
        
        print("评价指标对比:")
        print(f"正交性 (越小越好):")
        print(f"  默认参数: {default_orthogonality:.6f}")
        print(f"  优化参数: {optimized_orthogonality:.6f}")
        print(f"  改善: {((default_orthogonality - optimized_orthogonality) / default_orthogonality * 100):+.2f}%")
        
        print(f"频域分离度 (越大越好):")
        print(f"  默认参数: {default_freq_sep:.6f}")
        print(f"  优化参数: {optimized_freq_sep:.6f}")
        print(f"  改善: {((optimized_freq_sep - default_freq_sep) / default_freq_sep * 100):+.2f}%")
        
        print(f"重构误差 (越小越好):")
        print(f"  默认参数: {default_recon_error:.6f}")
        print(f"  优化参数: {optimized_recon_error:.6f}")
        print(f"  改善: {((default_recon_error - optimized_recon_error) / default_recon_error * 100):+.2f}%")
        
        # 总体评估
        improvements = 0
        if optimized_orthogonality < default_orthogonality:
            improvements += 1
        if optimized_freq_sep > default_freq_sep:
            improvements += 1
        if optimized_recon_error < default_recon_error:
            improvements += 1
            
        print(f"\n总体评估: {improvements}/3 个指标得到改善")
        
        if improvements >= 2:
            print("🎉 参数优化效果显著！")
        elif improvements >= 1:
            print("👍 参数优化有一定效果")
        else:
            print("⚠️ 参数优化效果不明显，可能需要调整优化策略")
        
        return {
            'optimization_successful': True,
            'best_params': best_params,
            'improvements': improvements,
            'convergence_curve': conv_curve
        }
        
    except Exception as e:
        print(f"参数优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'optimization_successful': False,
            'error': str(e)
        }

def main():
    """主测试函数"""
    print("开始参数优化功能测试...")
    
    results = test_parameter_optimization()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    if results['optimization_successful']:
        print("✅ 参数优化功能正常工作")
        print(f"✅ 优化改善了 {results['improvements']}/3 个评价指标")
    else:
        print("❌ 参数优化功能存在问题")
        print(f"❌ 错误信息: {results.get('error', '未知错误')}")
    
    return results

if __name__ == "__main__":
    main()
