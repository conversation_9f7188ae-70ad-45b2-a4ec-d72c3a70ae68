
%%  清空环境变量 来自公众号：《淘个代码》
warning off             % 关闭报警信息
close all               % 关闭开启的图窗
clear                   % 清空变量
clc  
addpath(genpath(pwd))

load Resize_Working_condition1.mat %加载数据集
resizeimg = Resize_Working_condition1';
input=resizeimg(:,1);
output=resizeimg(:,2);
numClasses = length(unique(cell2mat(output))); %分类个数
jg = 120;   %每组200个样本
% 创建索引
% 创建一个1x720的向量
i = 1:size(input);
% 创建一个和i同样大小的逻辑向量，所有元素默认为false
train_idx = false(size(i));
% 每120个元素中选择前84个作为训练集
for block = 1:numClasses
    start_idx = (block-1)*jg + 1;
    end_idx = block*jg;
    train_idx(start_idx:start_idx+round(0.7*jg)-1) = true;
end
% 获取训练集索引
training_set_idx = i(train_idx);
% 打印训练集索引
disp(training_set_idx);
% 创建一个和i同样大小的逻辑向量，所有元素默认为false
test_idx = false(size(i));
% 因为所有的索引要么是训练集要么是测试集，我们可以通过反转训练集的逻辑索引得到测试集的逻辑索引
test_idx = ~train_idx;
% 获取测试集索引
testing_set_idx = i(test_idx);
% 打印测试集索引
disp(testing_set_idx);
%% 划分训练集和测试集
input_train=[input(training_set_idx)];
input_test=[input(testing_set_idx)];

for i = 1:size(input_train,1)
    trainD(:,:,:,i) = input_train{i};
end
%训练集标签 
train_Y = categorical(cell2mat(output(training_set_idx)));
%测试集
for i = 1:size(input_test,1)
    testD(:,:,:,i) = input_test{i};
end
%测试集标签
test_Y =  categorical(cell2mat(output(testing_set_idx)));


%%
nnet = resnet18('Weights','none'); %调取resnet18网络，但是要将所有权重设置为初始值，以便于重新训练
layers = nnet.Layers(1:end-3); %选取resnet18的网络层
connections = nnet.Connections(1:end-3,:);%选取resnet18的链接方式
lgraph = CreateLgraphUsingConnections(layers,connections);%按照resnet18的方式重新组合网络
% 新建网络输入层，原始的resnet网络输入层大小是不一样的，这里要重新赋值一下
newInputLayer = imageInputLayer([size(input{1},1) size(input{1},2) size(input{1},3)],'Name','input1');
%将新的输入层替换原先的输入层
lgraph = replaceLayer(lgraph,"data",newInputLayer);

% Then, you need to connect the SE block properly
SEblockName = '2a';
ecaLayers = SEBlock(64,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'bn2a_branch2b', 'res2a/in1');
lgraph = connectLayers(lgraph, 'bn2a_branch2b', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'bn2a_branch2b', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'res2a/in1');


% Then, you need to connect the SE block properly
SEblockName = '2b';
ecaLayers = SEBlock(64,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'bn2b_branch2b', 'res2b/in1');
lgraph = connectLayers(lgraph, 'bn2b_branch2b', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'bn2b_branch2b', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'res2b/in1');


% Then, you need to connect the SE block properly
SEblockName = '3a';
ecaLayers = SEBlock(128,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'bn3a_branch2b', 'res3a/in1');
lgraph = connectLayers(lgraph, 'bn3a_branch2b', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'bn3a_branch2b', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'res3a/in1');


% Then, you need to connect the SE block properly
SEblockName = '3b';
ecaLayers = SEBlock(128,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'bn3b_branch2b', 'res3b/in1');
lgraph = connectLayers(lgraph, 'bn3b_branch2b', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'bn3b_branch2b', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'res3b/in1');



% Then, you need to connect the SE block properly
SEblockName = '4a';
ecaLayers = SEBlock(256,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'bn4a_branch2b', 'res4a/in1');
lgraph = connectLayers(lgraph, 'bn4a_branch2b', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'bn4a_branch2b', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'res4a/in1');


% Then, you need to connect the SE block properly
SEblockName = '4b';
ecaLayers = SEBlock(256,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'bn4b_branch2b', 'res4b/in1');
lgraph = connectLayers(lgraph, 'bn4b_branch2b', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'bn4b_branch2b', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'res4b/in1');


% Then, you need to connect the SE block properly
SEblockName = '5a';
ecaLayers = SEBlock(512,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'bn5a_branch2b', 'res5a/in1');
lgraph = connectLayers(lgraph, 'bn5a_branch2b', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'bn5a_branch2b', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'res5a/in1');


% Then, you need to connect the SE block properly
SEblockName = '5b';
ecaLayers = SEBlock(512,16,SEblockName); % Example parameters
lgraph = addLayers(lgraph, ecaLayers);
lgraph = disconnectLayers(lgraph, 'bn5b_branch2b', 'res5b/in1');
lgraph = connectLayers(lgraph, 'bn5b_branch2b', ['se_global_pool_',SEblockName]);
mul = multiplicationLayer(2,'Name',['scale_',SEblockName]);
lgraph = addLayers(lgraph, mul);
lgraph = connectLayers(lgraph, 'bn5b_branch2b', ['scale_',SEblockName,'/in1']);
lgraph = connectLayers(lgraph, ['se_sigmoid_',SEblockName],['scale_',SEblockName,'/in2']);
lgraph = connectLayers(lgraph, ['scale_',SEblockName], 'res5b/in1');

% DarkNet后3层接上GRU-Attention组成DarkNet-GRU-Attention
tempLayers = [
    % flattenLayer("Name","flatten")
    % gruLayer(NumNeurons,"Name","gru")
    % selfAttentionLayer(4,256,"Name","selfattention")
    fullyConnectedLayer(numClasses, 'Name', 'fc_out') % 新的全连接层
    softmaxLayer
    classificationLayer];
% 连接倒数第四层到新的flatten层
lgraph = addLayers(lgraph, tempLayers); % 将上述网络结构加入空白结构中
lgraph = connectLayers(lgraph, 'pool5', 'fc_out');

% analyzeNetwork(lgraph)
% % plot(lgraph)%绘制初始的RESNET18的网络结构

%% Set the hyper parameters for unet training
options = trainingOptions('adam', ...                 % 优化算法Adam
    'MaxEpochs', 10, ...                            % 最大训练次数
    'LearnRateSchedule','piecewise', ......  %动态调整学习率
    'LearnRateDropFactor',0.00001, ...  %学习率下降因子为0.00001
    'MiniBatchSize',32,...
    'GradientThreshold', 1, ...                       % 梯度阈值
    'InitialLearnRate', 0.001, ...         % 初始学习率
    'ExecutionEnvironment', 'auto',...                 % 训练环境
    'Verbose', 1, ...                                 % 关闭优化过程
    'Plots', 'none');                    % 画出曲线
% % start training
t0 = tic;  %开始计时
net = trainNetwork(trainD,train_Y, lgraph,options);
toc(t0); % 从t0开始到此处的执行时间 % 来自公众号：《淘个代码》


%%  查看网络结构
% save Working_condition0_trained_se_resnet18 net

pred = classify(net,testD);
accuracy=sum(test_Y==pred)/length(pred)



% 画方框图
figure('Position',[100 50 800 600])
confMat = confusionmat(test_Y,pred);  %test_Y是真实值标签
zjyanseplotConfMat(confMat.');
xlabel('Predicted label')
ylabel('Real label')
title(['SE-Resnet测试集正确率 = ',num2str(accuracy*100),' %'])

%% 原始样本分布
layerT = 'input1';
LayersTNeed = activations(net,trainD,layerT,'OutputAs','channels');% 支路1
RawFeature = [];
for i = 1:size(LayersTNeed,4)
    temp = reshape(LayersTNeed(:,:,:,i),1,[]);
    RawFeature = [RawFeature;temp];
end
tsne_data = tsne(RawFeature);

temp = [];
NumTypes = 10;  %故障类别数
for i = 1:size(train_Y,1)
headers = {'故障类别1';'故障类别2';'故障类别3';'故障类别4';'故障类别5';'故障类别6'};
cmap = hsv(NumTypes);
lable = double(train_Y);
% 计算训练集每类（第一类为例）多少种故障
str = headers(lable(i));
tempdata = categorical(cellstr(str));
temp = [temp;tempdata];
end
% 类别标签
species = temp;
% 二维图像
figure('Position',[100,50,1200,500])
subplot(1,2,1)
gscatter(tsne_data(:,1),tsne_data(:,2),species,cmap,'.',20,'on');
% 添加整张图的主标题
title('原始样本分布', 'Interpreter', 'none', 'FontSize', 14);
hold on 
%% 模型识别后样本分布
layerT = 'fc_out';
LayersTNeed = activations(net,trainD,layerT,'OutputAs','channels');% 支路1
RawFeature = [];
for i = 1:size(LayersTNeed,4)
    temp = reshape(LayersTNeed(:,:,:,i),1,[]);
    RawFeature = [RawFeature;temp];
end
tsne_data = tsne(RawFeature);

temp = [];
NumTypes = 10;  %故障类别数
for i = 1:size(train_Y,1)
headers = {'故障类别1';'故障类别2';'故障类别3';'故障类别4';'故障类别5';'故障类别6'};
cmap = hsv(NumTypes);
lable = double(train_Y);
% 计算训练集每类（第一类为例）多少种故障
str = headers(lable(i));
tempdata = categorical(cellstr(str));
temp = [temp;tempdata];
end

% 类别标签
species = temp;

% 二维图像
subplot(1,2,2)
gscatter(tsne_data(:,1),tsne_data(:,2),species,cmap,'.',20,'on');
% 添加整张图的主标题
title('SE-Resnet模型识别后样本分布', 'Interpreter', 'none', 'FontSize', 14);
hold off


