"""
快速测试改进后的故障诊断模型
专注于解决低准确率问题
"""

from fault_diagnosis_system import FaultDiagnosisSystem
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import numpy as np
from sklearn.model_selection import train_test_split

def main():
    print("=" * 60)
    print("快速测试改进后的故障诊断系统")
    print("=" * 60)
    
    # 初始化系统
    system = FaultDiagnosisSystem()
    
    try:
        # 1. 生成数据
        print("\n1. 数据生成")
        print("-" * 40)
        X, y, fault_types = system.generate_sample_data(
            num_classes=13, samples_per_class=20, signal_length=1024  # 减少信号长度提高速度
        )
        print(f"数据生成成功: {X.shape}")
        
        # 2. 数据分割
        print("\n2. 数据分割")
        print("-" * 40)
        X_train, X_temp, y_train, y_temp = train_test_split(
            X, y, test_size=0.4, random_state=42, stratify=y
        )
        X_val, X_test, y_val, y_test = train_test_split(
            X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp
        )
        
        print(f"训练集: {X_train.shape[0]} 样本")
        print(f"验证集: {X_val.shape[0]} 样本") 
        print(f"测试集: {X_test.shape[0]} 样本")
        
        # 3. 模型训练（使用改进的参数）
        print("\n3. 模型训练")
        print("-" * 40)
        
        training_history = system.train_model(
            X_train, y_train, X_val, y_val,
            epochs=30,  # 适中的训练轮数
            batch_size=8   # 小批次大小
        )
        
        # 4. 模型评估
        print("\n4. 模型评估")
        print("-" * 40)
        
        evaluation_results = system.evaluate_model(X_test, y_test, fault_types)
        
        # 5. 结果分析
        print("\n5. 改进效果分析")
        print("-" * 40)
        
        final_test_acc = evaluation_results['accuracy']
        final_train_acc = training_history['train_accs'][-1]
        final_val_acc = training_history['val_accs'][-1]
        
        print(f"✓ 最终测试准确率: {final_test_acc:.2f}%")
        print(f"✓ 最终训练准确率: {final_train_acc:.2f}%")
        print(f"✓ 最终验证准确率: {final_val_acc:.2f}%")
        
        # 改进效果评估
        if final_test_acc > 50:
            print("\n🎉 模型性能显著改善！准确率超过50%")
            status = "优秀"
        elif final_test_acc > 30:
            print("\n📈 模型性能有明显改善！准确率超过30%")
            status = "良好"
        elif final_test_acc > 15:
            print("\n⚡ 模型性能有所改善，但仍需优化")
            status = "一般"
        else:
            print("\n⚠️ 模型性能仍需进一步改进")
            status = "需要改进"
            
        # 6. 问题诊断和建议
        print(f"\n6. 诊断结果：{status}")
        print("-" * 40)
        
        if final_test_acc < 20:
            print("主要问题和解决建议：")
            print("1. 数据质量：检查并清理常数信号和异常值")
            print("2. 特征提取：优化VMD分解参数和SDP转换")
            print("3. 模型结构：调整网络深度和宽度")
            print("4. 训练策略：调整学习率、批次大小和训练轮数")
            print("5. 数据增强：增加有效的数据增强技术")
        elif final_test_acc < 40:
            print("进一步优化建议：")
            print("1. 增加训练数据量")
            print("2. 调整超参数（学习率、dropout等）")
            print("3. 尝试不同的优化器")
            print("4. 增加正则化技术")
        else:
            print("模型表现良好！可以考虑：")
            print("1. 进一步调优超参数")
            print("2. 尝试集成学习方法")
            print("3. 增加更多真实数据")
            
        return {
            'test_accuracy': final_test_acc,
            'train_accuracy': final_train_acc,
            'val_accuracy': final_val_acc,
            'status': status
        }
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = main()
    if results:
        print(f"\n测试完成！最终准确率: {results['test_accuracy']:.2f}%")
