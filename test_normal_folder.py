"""
测试新增的正常文件夹数据加载功能
验证是否能正确加载"正常"文件夹中的数据
"""

from fault_diagnosis_system import FaultDiagnosisSystem
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import numpy as np
import os

def test_normal_folder_loading():
    """测试正常文件夹数据加载"""
    print("=" * 60)
    print("测试正常文件夹数据加载功能")
    print("=" * 60)
    
    # 初始化系统
    system = FaultDiagnosisSystem()
    
    # 1. 检查正常文件夹是否存在
    print("\n1. 检查正常文件夹")
    print("-" * 40)
    
    normal_folder = "正常"
    if os.path.exists(normal_folder):
        print(f"✅ 正常文件夹存在: {normal_folder}")
        
        # 列出文件夹中的文件
        files = os.listdir(normal_folder)
        mat_files = [f for f in files if f.endswith('.mat')]
        
        print(f"文件夹中的文件: {files}")
        print(f"MAT文件数量: {len(mat_files)}")
        
        for mat_file in mat_files:
            file_path = os.path.join(normal_folder, mat_file)
            file_size = os.path.getsize(file_path)
            print(f"  - {mat_file}: {file_size/1024:.1f} KB")
    else:
        print(f"❌ 正常文件夹不存在: {normal_folder}")
        print("请确保在项目根目录创建了'正常'文件夹并放入正常电流数据")
        return False
    
    # 2. 测试数据加载
    print("\n2. 测试数据加载")
    print("-" * 40)
    
    try:
        # 生成数据（包含新的正常数据）
        X, y, fault_types = system.generate_sample_data(
            num_classes=13,
            samples_per_class=20,  # 少量样本用于测试
            signal_length=1024,
            use_external=False  # 只使用真实数据
        )
        
        print(f"✅ 数据加载成功")
        print(f"总数据形状: {X.shape}")
        print(f"故障类型数量: {len(fault_types)}")
        
        # 3. 分析正常数据
        print("\n3. 分析正常数据")
        print("-" * 40)
        
        # 找到正常数据
        normal_indices = np.where(y == 0)[0]  # 正常数据的标签是0
        normal_data = X[normal_indices]
        
        print(f"正常数据样本数: {len(normal_data)}")
        
        if len(normal_data) > 0:
            print(f"正常数据统计:")
            print(f"  均值: {np.mean(normal_data):.4f}")
            print(f"  标准差: {np.std(normal_data):.4f}")
            print(f"  最小值: {np.min(normal_data):.4f}")
            print(f"  最大值: {np.max(normal_data):.4f}")
            
            # 检查数据质量
            if np.std(normal_data) > 1e-6:
                print("✅ 正常数据质量良好（有变化）")
            else:
                print("⚠️ 正常数据可能是常数信号")
        
        # 4. 对比分析
        print("\n4. 各类别数据对比")
        print("-" * 40)
        
        print("各故障类型样本数量:")
        unique_labels, counts = np.unique(y, return_counts=True)
        for label, count in zip(unique_labels, counts):
            if label < len(fault_types):
                print(f"  {fault_types[label]}: {count} 样本")
        
        # 5. 数据可视化
        print("\n5. 数据可视化")
        print("-" * 40)
        
        import matplotlib.pyplot as plt
        
        # 绘制正常数据样本
        plt.figure(figsize=(15, 10))
        
        # 显示前几个正常数据样本
        num_samples_to_show = min(4, len(normal_data))
        
        for i in range(num_samples_to_show):
            plt.subplot(2, 2, i+1)
            t = np.linspace(0, len(normal_data[i])/1000, len(normal_data[i]))  # 假设采样率1kHz
            plt.plot(t, normal_data[i], 'b-', linewidth=1)
            plt.title(f'正常数据样本 {i+1}')
            plt.xlabel('时间 (s)')
            plt.ylabel('电流 (A)')
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('normal_data_samples.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("正常数据样本图已保存为: normal_data_samples.png")
        
        # 6. 与其他故障类型对比
        print("\n6. 与故障数据对比")
        print("-" * 40)
        
        plt.figure(figsize=(15, 8))
        
        # 显示不同类别的数据样本
        classes_to_show = min(6, len(fault_types))
        
        for i in range(classes_to_show):
            class_indices = np.where(y == i)[0]
            if len(class_indices) > 0:
                plt.subplot(2, 3, i+1)
                sample_data = X[class_indices[0]]
                t = np.linspace(0, len(sample_data)/1000, len(sample_data))
                plt.plot(t, sample_data, linewidth=1)
                plt.title(f'{fault_types[i]}')
                plt.xlabel('时间 (s)')
                plt.ylabel('电流 (A)')
                plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('fault_types_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("故障类型对比图已保存为: fault_types_comparison.png")
        
        return {
            'success': True,
            'total_samples': X.shape[0],
            'normal_samples': len(normal_data),
            'fault_types': fault_types,
            'data_quality': 'good' if np.std(normal_data) > 1e-6 else 'poor'
        }
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e)
        }

def test_data_distribution():
    """测试数据分布"""
    print("\n" + "=" * 60)
    print("数据分布分析")
    print("=" * 60)
    
    system = FaultDiagnosisSystem()
    
    try:
        # 加载数据
        X, y, fault_types = system.generate_sample_data(
            samples_per_class=30,
            signal_length=512,
            use_external=False
        )
        
        print(f"数据加载成功: {X.shape}")
        
        # 分析每个类别的数据特征
        print("\n各类别数据特征:")
        for class_idx in range(len(fault_types)):
            class_mask = y == class_idx
            class_data = X[class_mask]
            
            if len(class_data) > 0:
                class_mean = np.mean(class_data)
                class_std = np.std(class_data)
                class_range = [np.min(class_data), np.max(class_data)]
                
                status = "正常" if class_std > 1e-6 else "异常(常数)"
                print(f"{fault_types[class_idx]:15s}: 样本数={len(class_data):2d}, 均值={class_mean:7.3f}, 标准差={class_std:6.3f}, 状态={status}")
        
        # 检查类别平衡性
        unique_labels, counts = np.unique(y, return_counts=True)
        print(f"\n类别平衡性:")
        print(f"最小样本数: {np.min(counts)}")
        print(f"最大样本数: {np.max(counts)}")
        print(f"平均样本数: {np.mean(counts):.1f}")
        
        if np.max(counts) - np.min(counts) <= 5:
            print("✅ 类别分布均衡")
        else:
            print("⚠️ 类别分布不均衡")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据分布分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试正常文件夹数据加载功能...")
    
    # 1. 测试正常文件夹加载
    result1 = test_normal_folder_loading()
    
    # 2. 测试数据分布
    result2 = test_data_distribution()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if result1 and result1['success']:
        print("✅ 正常文件夹数据加载功能正常")
        print(f"✅ 总样本数: {result1['total_samples']}")
        print(f"✅ 正常样本数: {result1['normal_samples']}")
        print(f"✅ 数据质量: {result1['data_quality']}")
        print(f"✅ 故障类型数: {len(result1['fault_types'])}")
    else:
        print("❌ 正常文件夹数据加载存在问题")
        if result1 and 'error' in result1:
            print(f"❌ 错误: {result1['error']}")
    
    if result2:
        print("✅ 数据分布分析正常")
    else:
        print("❌ 数据分布分析存在问题")
    
    print("\n现在您的系统支持:")
    print("1. ✅ 从'正常'文件夹加载正常电流数据")
    print("2. ✅ 从'全部电流'文件夹补充正常数据")
    print("3. ✅ 13种故障类型完整检测")
    print("4. ✅ 自动数据质量检查")
    print("5. ✅ 数据可视化和对比分析")

if __name__ == "__main__":
    main()
