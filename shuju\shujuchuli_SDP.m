
clc;
clear;
addpath(genpath(pwd));
%%https://www.kaggle.com/datasets/sabermalek/imfds?select=x4z
%三相电压、三相电流、电机电流、电机转速和故障时间点。
%实验时间、实验次数和特征指数
load Preprocessed_Test_Data_Rotor_Current_Faild.mat    %REVD转子励磁电压断开
D=6;
a=train_data(:,:,D);
g=label_data;
%Rotor excitation voltage disconnection fault.
% 绘制x1和x2的波形
a = reshape(a, [], 1); % 先对data进行转置，变为3×322的矩阵，然后按列依次提取元素拼接成一个列向量，此时vector就是966×1的列向量
% plot(a(1:500,1), 'r'); 

load Preprocessed_Disconnect_Phase_10_11_21_.mat  %OP缺相
b=train_data(:,:,D);
b = reshape(b, [], 1); 
h=label_data;
% plot(b(1:1000,1), 'r'); 

load Preprocessed_Rotor_Current_Failed_R_.mat  %VREC转子励磁电流变化
c=train_data(:,:,D);
c = reshape(c, [], 1); 
i=label_data;
% plot( c(1:200,1:3), 'r'); 

load Preprocessed_Short_between_two_phases_.mat   %2PSC两相短路
d=train_data(:,:,D);
d = reshape(d, [], 1); 
j=label_data;

load Preprocessed_Test_Data_Short_phases_Ln_G_.mat    %1PSC单相对中性点短路
e=train_data(:,:,D);
e = reshape(e, [], 1); 
k=label_data;

load Preprocessed_No_failed.mat   %NF无故障
f=train_data(:,:,D);
f = reshape(f, [], 1); 
l=label_data;

% 一共是10个状态，每个状态有120组样本，每个样本的数据量大小为：1×2048
w=1000;                  % w是滑动窗口的大小1000
s=3000;                  % 每个故障表示有2048个故障点
m = 200;            %每种故障有120个样本

D0=[];
for i =1:m
    D0 = [D0,a(1+w*(i-1):w*(i-1)+s)];
end
D0 = D0';

D1=[];
for i =1:m
    D1 = [D1,b(1+w*(i-1):w*(i-1)+s)];
end
D1 = D1';

D2=[];
for i =1:m
    D2 = [D2,b(1+w*(i-1):w*(i-1)+s)];
end
D2= D2';

D3=[];
for i =1:m
    D3 = [D3,b(1+w*(i-1):w*(i-1)+s)];
end
D3= D3';

D4=[];
for i =1:m
    D4 = [D4,b(1+w*(i-1):w*(i-1)+s)];
end
D4= D4';

D5=[];
for i =1:m
    D5 = [D5,b(1+w*(i-1):w*(i-1)+s)];
end
D5= D5';

data = [D0;D1;D2;D3;D4;D5];
ceshi_data = data;

save data6 data


% folder='测试数据汇总/'; %%定义变量
% if exist(folder)==0 %%判断文件夹是否存在
%     mkdir(folder);  %%不存在时候，创建文件夹
% end
% 
% xlswrite('/测试数据汇总/转速1797_测试数据汇总.xlsx',ceshi_data);
% 
% dd = [];
% for i = 0:size(data,1)/m-1
%     dd(1+m*i:m+m*i) = i+1;
% end
% zj = [dd;data'];
% ceshi_data = zj';
% xlswrite('/测试数据汇总/转速1797_测试数据汇总带标签.xlsx',ceshi_data);
% rmpath(genpath(pwd))
% 
% 
