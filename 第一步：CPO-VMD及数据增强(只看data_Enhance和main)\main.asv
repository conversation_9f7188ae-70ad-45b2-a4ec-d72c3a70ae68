clear all
close all
clc
warning off

%% 加载信号
% signal=xlsread('data.xlsx');
load shuju/ib1000_2.mat;                 a1=ib1000_2; %1   600  800   1000
load shuju/ob1000_2.mat;                 a2=ob1000_2; %2
load shuju/tb1000_2.mat;                 a3=tb1000_2; %3
load shuju/n1000_3_2.mat;                a4=n1000_3_2;%4
%% 图像绘制
fs = 50000; %采样频率
L=2048;%采样点数
a=5000;%间隔
%----------------导入ds故障的数据-----------------------------------------
x_input=a4(a:L+a) ;%DWQ  DDS
N=length(x_input);              %数据长度
n=0:N-1;  
t=n/fs; %生成每个数据点对应的时间戳
% y=x_input;
% figure(1); %时域图像
% subplot(2,1,1);
% plot(t,y);
% xlabel('时间');
% ylabel('幅值');
signal=x_input;
%% CPO-VMD分解
%% 参数设置
data=signal;
len=length(data);
f=data(1:len);


% alpha = 2000;        % moderate bandwidth constraint
tau = 0;            % noise-tolerance (no strict fidelity enforcement)
% K = 4;              % 4 modes
DC = 0;             % no DC part imposed
init = 1;           % initialize omegas uniformly
tol = 1e-7;
 
%% 普通VMD分解
%[u, u_hat, omega] = VMD(f, alpha, tau, K, DC, init, tol);
% 分解
[u1, u_hat1, omega1,curve,Target_pos] = WLVMD(f, tau, DC, init, tol);
figure
plot(curve,'linewidth',1.5);
title('收敛曲线')
xlabel('迭代次数')
ylabel('适应度值')
grid on

%分解
figure
subplot(size(u1,1)+1,1,1);
plot(f,'k');grid on;
title('原始数据');
for i = 1:size(u1,1)
    subplot(size(u1,1)+1,1,i+1);
    plot(u1(i,:),'k');
end

disp(['最优K值为：',num2str(Target_pos(2))])
disp(['最优alpha值为：',num2str(Target_pos(1))])
disp(['最优综合指标为：',num2str(min(curve))])
%% 计时结束
%% 频域图
[m,n]=size(u1);
imf=u1;
len=length(imf);%信号长度
fs=5000;%采样频率
% 采样时间
t = (0:len-1)/fs; 
figure
for i=1:m
subplot(m,1,i)
[cc,y_f]=hua_fft_1(imf(i,:),fs,1);
a1(i,:)=cc;
plot(y_f,cc,'k','LineWIdth',1.5);
% hua_fft_1(u(i,:),fs,1)
ylabel(['imf',num2str(i)]);
axis tight
end
xlabel('频率/Hz')

%% 分解结果整合
u=[IMF(2:end,:);imf];
plot_func(signal, u)
