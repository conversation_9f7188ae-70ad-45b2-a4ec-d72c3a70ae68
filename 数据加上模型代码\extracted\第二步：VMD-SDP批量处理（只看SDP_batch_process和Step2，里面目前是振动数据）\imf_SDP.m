function imf_SDP(imf)
%--------------- Run actual VMD code:数据进行vmd分解---------------------------
%SDP参数
imf_num = size(imf,2); % imf分量个数
F = 360/imf_num; % 镜像对称平面旋转角
L = 2;  % 时间间隔参数 （取[1,10]之间可以表示信号之间的细微差别                     
G = F/2; % 放大角度（一般小于等于F镜像对称平面旋转角
%SDP绘制
color_map = hsv(imf_num*2); % 使用hsv颜色映射
marker_size = 10; % 符号大小
  for i = 1:imf_num
    da = imf(:,i);
    rs = (da - min(da))/(max(da) - min(da)); % 极坐标半径
    th = F*i + rs*G; % 逆时针转角
    ph = F*i - rs*G; % 顺时针转角
    th = th(L:end);
    ph = ph(L:end);
    rs = rs(1:end-L+1);
    color_index = i; % 计算颜色索引
    polarscatter(th*(pi/180), rs, marker_size, 'filled', 'MarkerFaceColor', color_map(color_index,:));
    hold on;
    polarscatter(ph*(pi/180), rs, marker_size, 'filled', 'MarkerFaceColor', color_map(color_index,:));
  end 
end