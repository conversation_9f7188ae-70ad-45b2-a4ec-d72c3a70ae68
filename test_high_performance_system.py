"""
测试高性能六相永磁同步电机故障诊断系统
目标：将准确率提升到95%以上
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from fault_diagnosis_system import FaultDiagnosisSystem
from high_performance_fault_diagnosis import HighPerformanceFaultDiagnosis
import warnings
warnings.filterwarnings('ignore')

def test_high_performance_system():
    """测试高性能故障诊断系统"""
    print("=" * 80)
    print("高性能六相永磁同步电机故障诊断系统测试")
    print("目标：将准确率从29%提升到95%以上")
    print("=" * 80)
    
    # 1. 加载真实数据
    print("\n1. 加载真实故障数据")
    print("-" * 50)
    
    # 使用原有系统加载数据
    original_system = FaultDiagnosisSystem()
    X, y, fault_types = original_system.generate_sample_data(
        num_classes=13,
        samples_per_class=50,  # 增加样本数量
        signal_length=1024,    # 适中的信号长度
        use_external=False     # 只使用真实数据
    )
    
    print(f"原始数据集: {X.shape}")
    print(f"故障类型: {fault_types}")
    
    # 数据质量检查
    print("\n数据质量检查:")
    for class_idx in range(len(fault_types)):
        class_mask = y == class_idx
        class_data = X[class_mask]
        
        if len(class_data) > 0:
            class_std = np.std(class_data)
            status = "正常" if class_std > 1e-6 else "异常(常数)"
            print(f"  {fault_types[class_idx]:15s}: {len(class_data):3d} 样本, 标准差={class_std:8.4f} - {status}")
    
    # 2. 初始化高性能系统
    print("\n2. 初始化高性能故障诊断系统")
    print("-" * 50)
    
    hp_system = HighPerformanceFaultDiagnosis()
    
    # 3. 训练高性能模型
    print("\n3. 训练高性能模型")
    print("-" * 50)
    
    try:
        results = hp_system.train_high_performance_model(
            X=X,
            y=y,
            fault_types=fault_types,
            test_size=0.2,
            epochs=100,  # 适中的训练轮数
            batch_size=16,
            learning_rate=0.0001
        )
        
        final_accuracy = results['final_accuracy']
        
        print(f"\n🎯 最终测试准确率: {final_accuracy:.2f}%")
        
        # 4. 结果分析
        print("\n4. 性能提升分析")
        print("-" * 50)
        
        baseline_accuracy = 29.17  # 原始系统准确率
        improvement = final_accuracy - baseline_accuracy
        improvement_ratio = (improvement / baseline_accuracy) * 100
        
        print(f"基线准确率 (原系统): {baseline_accuracy:.2f}%")
        print(f"高性能系统准确率: {final_accuracy:.2f}%")
        print(f"绝对提升: {improvement:+.2f}%")
        print(f"相对提升: {improvement_ratio:+.1f}%")
        
        # 5. 目标达成评估
        print("\n5. 目标达成评估")
        print("-" * 50)
        
        target_accuracy = 95.0
        
        if final_accuracy >= target_accuracy:
            print(f"🎉 恭喜！已达成目标准确率 {target_accuracy}%")
            print(f"✅ 实际准确率: {final_accuracy:.2f}%")
            achievement = "完全达成"
        elif final_accuracy >= 80.0:
            print(f"👍 接近目标！准确率已达到 {final_accuracy:.2f}%")
            print(f"📈 距离目标还差: {target_accuracy - final_accuracy:.2f}%")
            achievement = "接近目标"
        elif final_accuracy >= 60.0:
            print(f"📈 显著改善！准确率提升到 {final_accuracy:.2f}%")
            print(f"🔧 仍需进一步优化")
            achievement = "显著改善"
        elif final_accuracy >= 40.0:
            print(f"⚡ 有所改善！准确率提升到 {final_accuracy:.2f}%")
            print(f"🔧 需要更多优化策略")
            achievement = "有所改善"
        else:
            print(f"⚠️ 改善有限，准确率为 {final_accuracy:.2f}%")
            print(f"🔧 需要重新审视策略")
            achievement = "改善有限"
        
        # 6. 绘制训练历史
        print("\n6. 绘制训练历史")
        print("-" * 50)
        
        plt.figure(figsize=(15, 10))
        
        # 训练损失
        plt.subplot(2, 3, 1)
        plt.plot(results['train_losses'], 'b-', linewidth=2, label='训练损失')
        plt.title('训练损失曲线')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        
        # 训练和验证准确率
        plt.subplot(2, 3, 2)
        plt.plot(results['train_accs'], 'b-', linewidth=2, label='训练准确率')
        plt.plot(results['val_accs'], 'r-', linewidth=2, label='验证准确率')
        plt.title('准确率曲线')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy (%)')
        plt.legend()
        plt.grid(True)
        
        # 性能对比
        plt.subplot(2, 3, 3)
        systems = ['原系统', '高性能系统']
        accuracies = [baseline_accuracy, final_accuracy]
        colors = ['lightcoral', 'lightgreen']
        
        bars = plt.bar(systems, accuracies, color=colors, alpha=0.7)
        plt.title('系统性能对比')
        plt.ylabel('准确率 (%)')
        plt.ylim(0, 100)
        
        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 添加目标线
        plt.axhline(y=target_accuracy, color='red', linestyle='--', 
                   label=f'目标 ({target_accuracy}%)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 改善效果
        plt.subplot(2, 3, 4)
        categories = ['绝对提升', '相对提升']
        improvements = [improvement, improvement_ratio]
        colors = ['skyblue', 'orange']
        
        bars = plt.bar(categories, improvements, color=colors, alpha=0.7)
        plt.title('性能提升效果')
        plt.ylabel('提升幅度')
        
        for bar, imp in zip(bars, improvements):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{imp:+.1f}{"%" if "相对" in categories[improvements.index(imp)] else "%"}',
                    ha='center', va='bottom', fontweight='bold')
        
        plt.grid(True, alpha=0.3)
        
        # 技术改进总结
        plt.subplot(2, 3, 5)
        plt.text(0.1, 0.9, '技术改进策略:', fontsize=14, fontweight='bold', transform=plt.gca().transAxes)
        improvements_text = [
            '✓ 高级数据增强 (8倍增强)',
            '✓ 合成数据生成 (30%合成数据)',
            '✓ 多域特征提取 (时域+频域+时频域)',
            '✓ 改进SSE-Transformer模型',
            '✓ 标签平滑损失函数',
            '✓ 自适应学习率调度',
            '✓ 梯度裁剪和正则化',
            '✓ 早停机制'
        ]
        
        for i, text in enumerate(improvements_text):
            plt.text(0.1, 0.8 - i*0.08, text, fontsize=10, transform=plt.gca().transAxes)
        
        plt.axis('off')
        
        # 结果总结
        plt.subplot(2, 3, 6)
        plt.text(0.1, 0.9, '结果总结:', fontsize=14, fontweight='bold', transform=plt.gca().transAxes)
        summary_text = [
            f'原始准确率: {baseline_accuracy:.1f}%',
            f'最终准确率: {final_accuracy:.1f}%',
            f'绝对提升: {improvement:+.1f}%',
            f'相对提升: {improvement_ratio:+.1f}%',
            f'目标达成: {achievement}',
            '',
            '数据增强效果显著！' if final_accuracy > 50 else '需要进一步优化',
            '模型架构改进有效！' if final_accuracy > baseline_accuracy * 1.5 else '模型仍需调整'
        ]
        
        for i, text in enumerate(summary_text):
            color = 'green' if '✓' in text or final_accuracy > 70 else 'black'
            plt.text(0.1, 0.8 - i*0.08, text, fontsize=10, 
                    transform=plt.gca().transAxes, color=color)
        
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig('high_performance_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("训练历史和结果分析图已保存为: high_performance_results.png")
        
        return {
            'success': True,
            'final_accuracy': final_accuracy,
            'improvement': improvement,
            'achievement': achievement,
            'target_reached': final_accuracy >= target_accuracy
        }
        
    except Exception as e:
        print(f"❌ 高性能系统训练失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """主测试函数"""
    print("开始高性能故障诊断系统测试...")
    
    results = test_high_performance_system()
    
    print("\n" + "=" * 80)
    print("测试完成总结")
    print("=" * 80)
    
    if results['success']:
        print("✅ 高性能系统测试成功完成")
        print(f"✅ 最终准确率: {results['final_accuracy']:.2f}%")
        print(f"✅ 性能提升: {results['improvement']:+.2f}%")
        print(f"✅ 达成状态: {results['achievement']}")
        
        if results['target_reached']:
            print("🎉 恭喜！已成功达到95%的目标准确率！")
        else:
            print("📈 虽未完全达到95%目标，但已实现显著改善")
            
        print("\n🔧 系统优化建议:")
        if results['final_accuracy'] < 95:
            print("• 增加更多真实故障数据")
            print("• 调整数据增强策略")
            print("• 优化模型超参数")
            print("• 尝试集成学习方法")
        else:
            print("• 系统已达到优秀性能水平")
            print("• 可考虑部署到生产环境")
            print("• 持续监控和改进")
    else:
        print("❌ 高性能系统测试失败")
        print(f"❌ 错误信息: {results.get('error', '未知错误')}")
        print("\n🔧 故障排除建议:")
        print("• 检查数据格式和质量")
        print("• 确认依赖库安装正确")
        print("• 检查GPU/CPU资源")
        print("• 调整模型参数")

if __name__ == "__main__":
    main()
