#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据增强功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fault_diagnosis_system import FaultDiagnosisSystem
import matplotlib.pyplot as plt
import numpy as np

def test_data_augmentation():
    """测试数据增强功能"""
    print("=" * 50)
    print("测试数据增强功能")
    print("=" * 50)
    
    # 初始化系统
    system = FaultDiagnosisSystem()
    
    try:
        print("1. 开始数据增强演示...")
        seed_signals, enhanced_signals = system.plot_data_augmentation_demo()
        
        print("✓ 数据增强演示成功完成")
        print(f"种子信号形状: {seed_signals.shape}")
        print(f"增强信号形状: {enhanced_signals.shape}")
        
        # 验证数据形状
        assert seed_signals.shape[0] == 500, f"种子信号时间点数应为500，实际为{seed_signals.shape[0]}"
        assert seed_signals.shape[1] == 5, f"种子信号数量应为5，实际为{seed_signals.shape[1]}"
        
        # 验证增强信号的形状
        expected_enhanced_shape = (5, 500 * 5)  # (信号数, 时间点数 * 扩展倍数)
        assert enhanced_signals.shape == expected_enhanced_shape, \
            f"增强信号形状应为{expected_enhanced_shape}，实际为{enhanced_signals.shape}"
        
        print("✓ 所有形状验证通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据增强演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_data_augmentation()
    if success:
        print("\n" + "=" * 50)
        print("测试成功完成！")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("测试失败！")
        print("=" * 50)
        sys.exit(1)
