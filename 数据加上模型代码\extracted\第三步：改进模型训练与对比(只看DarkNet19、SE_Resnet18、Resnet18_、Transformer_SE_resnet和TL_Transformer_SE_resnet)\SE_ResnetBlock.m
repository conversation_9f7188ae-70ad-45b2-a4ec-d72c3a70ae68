function layers = SE_ResnetBlock(C, r,num)
% C is the number of channels in the previous layer
% r is the reduction ratio for the SE block
    reducedDims = max(floor(C / r), 1); % Ensure at least one dimension
    load se_resnet18.mat
% 将优化目标参数传进来的值 转换为需要的超参数
net = se_resnet18;
% 获取除了最后三层之外的所有层
layersTransfer = net.Layers(2:end-3);
connections = net.Connections(2:83,:);
connections =[connections; net.Connections(85:end-2,:)];%选取resnet18的链接方式
layersTransfer = CreateLgraphUsingConnections(layersTransfer,connections);%按照resnet18的方式重新组合网络
% 添加图像输入层
layersTransfer = addLayers(layersTransfer,  imageInputLayer([size(input{1},1) size(input{1},2) size(input{1},3)],'Name','input1'));
layersTransfer = connectLayers(layersTransfer, 'input1', 'conv1');
    tempLayers = [
        globalAveragePooling2dLayer( 'Name', ['se_global_pool_',num])
        % averagePooling2dLayer(1, 'Stride', 1, 'Name', ['se_avg_pool_',num], 'Padding', 'same')
        fullyConnectedLayer(reducedDims, 'Name', ['se_fc1_',num])
        reluLayer('Name', ['se_relu_',num])
        fullyConnectedLayer(C, 'Name', ['se_fc2_',num])
        sigmoidLayer('Name', ['se_sigmoid_',num])
    ];
     layersTransfer= addLayers(layersTransfer, tempLayers); % 将上述网络结构加入空白结构中
end