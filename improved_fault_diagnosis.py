"""
改进的六相永磁同步电机故障诊断系统
目标: 将准确率从29%提升到95%以上

主要改进策略:
1. 增强数据预处理和特征工程
2. 改进模型架构和训练策略
3. 使用集成学习和数据增强
4. 优化超参数和正则化
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.utils.class_weight import compute_class_weight
import matplotlib.pyplot as plt
import seaborn as sns
from fault_diagnosis_system import FaultDiagnosisSystem
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedFaultDiagnosis(FaultDiagnosisSystem):
    """改进的故障诊断系统"""
    
    def __init__(self):
        super().__init__()
        self.scaler = RobustScaler()  # 使用更鲁棒的标准化
        self.feature_selector = None
        self.ensemble_models = []
        
    def enhanced_data_preprocessing(self, X, y):
        """增强的数据预处理"""
        print("执行增强数据预处理...")
        
        # 1. 数据清理 - 更严格的标准
        X_clean, y_clean = self.advanced_data_cleaning(X, y)
        
        # 2. 特征工程
        X_features = self.extract_comprehensive_features(X_clean)
        
        # 3. 特征选择
        X_selected = self.feature_selection(X_features, y_clean)
        
        # 4. 数据标准化
        X_scaled = self.scaler.fit_transform(X_selected)
        
        # 5. 数据增强
        X_augmented, y_augmented = self.advanced_data_augmentation(X_scaled, y_clean)
        
        return X_augmented, y_augmented
    
    def advanced_data_cleaning(self, X, y):
        """高级数据清理"""
        print("  - 执行高级数据清理")
        
        valid_indices = []
        
        for i, signal in enumerate(X):
            # 检查信号质量
            signal_std = np.std(signal)
            signal_mean = np.mean(signal)
            signal_range = np.ptp(signal)  # peak-to-peak
            
            # 更严格的质量标准
            if (signal_std > 1e-4 and  # 标准差足够大
                signal_range > 1e-4 and  # 范围足够大
                not np.any(np.isnan(signal)) and  # 无NaN
                not np.any(np.isinf(signal)) and  # 无Inf
                np.abs(signal_mean) < 1000 and  # 均值合理
                signal_std < 1000):  # 标准差合理
                
                # 检查信号的复杂度
                # 计算信号的近似熵
                approx_entropy = self.calculate_approximate_entropy(signal)
                if approx_entropy > 0.1:  # 信号有足够的复杂度
                    valid_indices.append(i)
        
        print(f"    原始样本: {len(X)}, 清理后: {len(valid_indices)}")
        
        if len(valid_indices) == 0:
            print("    警告: 没有有效样本!")
            return X, y
        
        X_clean = X[valid_indices]
        y_clean = y[valid_indices]
        
        # 重新映射标签
        unique_labels = np.unique(y_clean)
        label_mapping = {old: new for new, old in enumerate(unique_labels)}
        y_remapped = np.array([label_mapping[label] for label in y_clean])
        
        return X_clean, y_remapped
    
    def calculate_approximate_entropy(self, signal, m=2, r=None):
        """计算近似熵"""
        N = len(signal)
        if r is None:
            r = 0.2 * np.std(signal)
        
        def _maxdist(xi, xj, N, m):
            return max([abs(ua - va) for ua, va in zip(xi, xj)])
        
        def _phi(m):
            patterns = np.array([signal[i:i+m] for i in range(N - m + 1)])
            C = np.zeros(N - m + 1)
            
            for i in range(N - m + 1):
                template_i = patterns[i]
                for j in range(N - m + 1):
                    if _maxdist(template_i, patterns[j], N, m) <= r:
                        C[i] += 1.0
            
            phi = (N - m + 1.0) ** (-1) * sum(np.log(C / (N - m + 1.0)))
            return phi
        
        return _phi(m) - _phi(m + 1)
    
    def extract_comprehensive_features(self, X):
        """提取综合特征"""
        print("  - 提取综合特征")
        
        features_list = []
        
        for signal in X:
            # 1. 时域特征
            time_features = self.extract_time_domain_features(signal)
            
            # 2. 频域特征
            freq_features = self.extract_frequency_domain_features(signal)
            
            # 3. 时频域特征
            tf_features = self.extract_time_frequency_features(signal)
            
            # 4. 非线性特征
            nonlinear_features = self.extract_nonlinear_features(signal)
            
            # 5. SDP特征
            sdp_features = self.extract_sdp_features(signal)
            
            # 合并所有特征
            all_features = np.concatenate([
                time_features, freq_features, tf_features, 
                nonlinear_features, sdp_features
            ])
            
            features_list.append(all_features)
        
        features_array = np.array(features_list)
        print(f"    提取特征维度: {features_array.shape[1]}")
        
        return features_array
    
    def extract_time_domain_features(self, signal):
        """提取时域特征"""
        features = []
        
        # 基本统计特征
        features.extend([
            np.mean(signal),           # 均值
            np.std(signal),            # 标准差
            np.var(signal),            # 方差
            np.max(signal),            # 最大值
            np.min(signal),            # 最小值
            np.ptp(signal),            # 峰峰值
            np.median(signal),         # 中位数
            np.mean(np.abs(signal)),   # 平均绝对值
            np.sqrt(np.mean(signal**2)) # RMS
        ])
        
        # 形状特征
        features.extend([
            self.skewness(signal),     # 偏度
            self.kurtosis(signal),     # 峰度
            self.crest_factor(signal), # 波峰因子
            self.clearance_factor(signal), # 裕度因子
            self.impulse_factor(signal),   # 脉冲因子
            self.shape_factor(signal)      # 波形因子
        ])
        
        return np.array(features)
    
    def extract_frequency_domain_features(self, signal):
        """提取频域特征"""
        # FFT
        fft_signal = np.fft.fft(signal)
        magnitude = np.abs(fft_signal[:len(signal)//2])
        freqs = np.fft.fftfreq(len(signal), 1.0)[:len(signal)//2]
        
        features = []
        
        # 频域统计特征
        features.extend([
            np.mean(magnitude),        # 频域均值
            np.std(magnitude),         # 频域标准差
            np.max(magnitude),         # 最大幅值
            freqs[np.argmax(magnitude)] if len(magnitude) > 0 else 0, # 主频率
            np.sum(magnitude),         # 总能量
        ])
        
        # 功率谱密度特征
        psd = magnitude ** 2
        total_power = np.sum(psd) + 1e-10
        
        # 频带能量比
        low_freq_power = np.sum(psd[freqs < 0.1])
        mid_freq_power = np.sum(psd[(freqs >= 0.1) & (freqs < 0.3)])
        high_freq_power = np.sum(psd[freqs >= 0.3])
        
        features.extend([
            low_freq_power / total_power,
            mid_freq_power / total_power,
            high_freq_power / total_power
        ])
        
        # 频域重心和带宽
        freq_centroid = np.sum(freqs * magnitude) / (np.sum(magnitude) + 1e-10)
        freq_bandwidth = np.sqrt(np.sum(((freqs - freq_centroid) ** 2) * magnitude) / (np.sum(magnitude) + 1e-10))
        
        features.extend([freq_centroid, freq_bandwidth])
        
        return np.array(features)
    
    def extract_time_frequency_features(self, signal):
        """提取时频域特征"""
        features = []
        
        # 小波变换特征
        try:
            from scipy import signal as scipy_signal
            
            # 连续小波变换
            widths = np.arange(1, 31)
            cwt_matrix = scipy_signal.cwt(signal, scipy_signal.ricker, widths)
            
            # 小波能量特征
            for i in range(0, len(widths), 5):
                scale_energy = np.sum(np.abs(cwt_matrix[i:i+5])**2)
                features.append(scale_energy)
            
            # 瞬时特征
            analytic_signal = scipy_signal.hilbert(signal)
            instantaneous_amplitude = np.abs(analytic_signal)
            instantaneous_frequency = np.diff(np.unwrap(np.angle(analytic_signal)))
            
            features.extend([
                np.mean(instantaneous_amplitude),
                np.std(instantaneous_amplitude),
                np.mean(instantaneous_frequency),
                np.std(instantaneous_frequency)
            ])
            
        except Exception:
            # 如果小波变换失败，用简单的时频特征替代
            features = [0] * 10
        
        return np.array(features)
    
    def extract_nonlinear_features(self, signal):
        """提取非线性特征"""
        features = []
        
        # 近似熵
        approx_entropy = self.calculate_approximate_entropy(signal)
        features.append(approx_entropy)
        
        # 样本熵
        sample_entropy = self.calculate_sample_entropy(signal)
        features.append(sample_entropy)
        
        # Lyapunov指数的简化估计
        lyapunov_exp = self.estimate_lyapunov_exponent(signal)
        features.append(lyapunov_exp)
        
        return np.array(features)
    
    def calculate_sample_entropy(self, signal, m=2, r=None):
        """计算样本熵"""
        N = len(signal)
        if r is None:
            r = 0.2 * np.std(signal)
        
        def _maxdist(xi, xj):
            return max([abs(ua - va) for ua, va in zip(xi, xj)])
        
        def _phi(m):
            patterns = np.array([signal[i:i+m] for i in range(N - m + 1)])
            C = 0
            
            for i in range(N - m):
                template_i = patterns[i]
                for j in range(i + 1, N - m + 1):
                    if _maxdist(template_i, patterns[j]) <= r:
                        C += 1
            
            return C
        
        A = _phi(m + 1)
        B = _phi(m)
        
        if B == 0:
            return 0
        else:
            return -np.log(A / B)
    
    def estimate_lyapunov_exponent(self, signal):
        """估计Lyapunov指数"""
        try:
            # 简化的Lyapunov指数估计
            diff_signal = np.diff(signal)
            if len(diff_signal) == 0:
                return 0
            
            # 计算相邻点的发散率
            divergence = np.mean(np.abs(diff_signal))
            return np.log(divergence + 1e-10)
        except:
            return 0
    
    def extract_sdp_features(self, signal):
        """提取SDP特征"""
        try:
            # 执行VMD分解
            u, _, _ = self.vmd_decomposer.vmd(signal)
            
            # 计算SDP
            sdp_data, _ = self.sdp_processor.compute_sdp(u, use_color=False)
            
            # 从SDP数据中提取特征
            features = []
            for data in sdp_data:
                features.extend([
                    np.mean(data['rs']),
                    np.std(data['rs']),
                    np.max(data['rs']),
                    np.min(data['rs'])
                ])
            
            # 确保特征长度一致
            if len(features) < 20:
                features.extend([0] * (20 - len(features)))
            else:
                features = features[:20]
                
            return np.array(features)
        except:
            return np.zeros(20)
    
    def skewness(self, signal):
        """计算偏度"""
        mean = np.mean(signal)
        std = np.std(signal)
        if std == 0:
            return 0
        return np.mean(((signal - mean) / std) ** 3)
    
    def kurtosis(self, signal):
        """计算峰度"""
        mean = np.mean(signal)
        std = np.std(signal)
        if std == 0:
            return 0
        return np.mean(((signal - mean) / std) ** 4) - 3
    
    def crest_factor(self, signal):
        """计算波峰因子"""
        rms = np.sqrt(np.mean(signal**2))
        if rms == 0:
            return 0
        return np.max(np.abs(signal)) / rms
    
    def clearance_factor(self, signal):
        """计算裕度因子"""
        mean_sqrt_abs = np.mean(np.sqrt(np.abs(signal))) ** 2
        if mean_sqrt_abs == 0:
            return 0
        return np.max(np.abs(signal)) / mean_sqrt_abs
    
    def impulse_factor(self, signal):
        """计算脉冲因子"""
        mean_abs = np.mean(np.abs(signal))
        if mean_abs == 0:
            return 0
        return np.max(np.abs(signal)) / mean_abs
    
    def shape_factor(self, signal):
        """计算波形因子"""
        rms = np.sqrt(np.mean(signal**2))
        mean_abs = np.mean(np.abs(signal))
        if mean_abs == 0:
            return 0
        return rms / mean_abs

    def feature_selection(self, X, y):
        """特征选择"""
        print("  - 执行特征选择")

        # 使用随机森林进行特征重要性评估
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)

        # 获取特征重要性
        feature_importance = rf.feature_importances_

        # 选择重要性大于阈值的特征
        threshold = np.mean(feature_importance)
        selected_features = feature_importance > threshold

        print(f"    原始特征数: {X.shape[1]}, 选择特征数: {np.sum(selected_features)}")

        self.feature_selector = selected_features
        return X[:, selected_features]

    def advanced_data_augmentation(self, X, y, augmentation_factor=5):
        """高级数据增强"""
        print("  - 执行高级数据增强")

        X_augmented = [X]
        y_augmented = [y]

        for _ in range(augmentation_factor):
            # 添加噪声
            noise_factor = np.random.uniform(0.01, 0.05)
            X_noise = X + np.random.normal(0, noise_factor, X.shape)

            # 特征缩放
            scale_factor = np.random.uniform(0.9, 1.1)
            X_scaled = X * scale_factor

            # 特征偏移
            shift_factor = np.random.uniform(-0.1, 0.1)
            X_shifted = X + shift_factor * np.std(X, axis=0)

            X_augmented.extend([X_noise, X_scaled, X_shifted])
            y_augmented.extend([y, y, y])

        X_final = np.vstack(X_augmented)
        y_final = np.hstack(y_augmented)

        print(f"    数据增强: {X.shape[0]} -> {X_final.shape[0]} 样本")

        return X_final, y_final

    def create_improved_model(self, input_size, num_classes):
        """创建改进的模型"""
        class ImprovedSSETransformer(nn.Module):
            def __init__(self, input_size, num_classes, dropout=0.3):
                super().__init__()

                # 特征提取层
                self.feature_extractor = nn.Sequential(
                    nn.Linear(input_size, 512),
                    nn.BatchNorm1d(512),
                    nn.ReLU(),
                    nn.Dropout(dropout),

                    nn.Linear(512, 256),
                    nn.BatchNorm1d(256),
                    nn.ReLU(),
                    nn.Dropout(dropout),

                    nn.Linear(256, 128),
                    nn.BatchNorm1d(128),
                    nn.ReLU(),
                    nn.Dropout(dropout)
                )

                # 注意力机制
                self.attention = nn.MultiheadAttention(128, 8, dropout=dropout)

                # 分类器
                self.classifier = nn.Sequential(
                    nn.Linear(128, 64),
                    nn.BatchNorm1d(64),
                    nn.ReLU(),
                    nn.Dropout(dropout),
                    nn.Linear(64, num_classes)
                )

            def forward(self, x):
                # 特征提取
                features = self.feature_extractor(x)

                # 注意力机制 (需要调整维度)
                features_reshaped = features.unsqueeze(0)  # (1, batch, features)
                attended_features, _ = self.attention(features_reshaped, features_reshaped, features_reshaped)
                attended_features = attended_features.squeeze(0)  # (batch, features)

                # 分类
                output = self.classifier(attended_features)
                return output

        return ImprovedSSETransformer(input_size, num_classes)

    def train_improved_model(self, X, y, fault_types, test_size=0.2, epochs=100, batch_size=32):
        """训练改进的模型"""
        print("=" * 60)
        print("训练改进的故障诊断模型")
        print("=" * 60)

        # 1. 数据预处理
        X_processed, y_processed = self.enhanced_data_preprocessing(X, y)

        # 2. 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X_processed, y_processed, test_size=test_size,
            random_state=42, stratify=y_processed
        )

        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=0.2,
            random_state=42, stratify=y_train
        )

        print(f"训练集: {X_train.shape}")
        print(f"验证集: {X_val.shape}")
        print(f"测试集: {X_test.shape}")

        # 3. 计算类别权重
        class_weights = compute_class_weight('balanced',
                                           classes=np.unique(y_train),
                                           y=y_train)
        class_weights_tensor = torch.FloatTensor(class_weights).to(self.device)

        # 4. 创建数据加载器
        train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.LongTensor(y_train))
        val_dataset = TensorDataset(torch.FloatTensor(X_val), torch.LongTensor(y_val))
        test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.LongTensor(y_test))

        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

        # 5. 创建模型
        num_classes = len(np.unique(y_processed))
        self.model = self.create_improved_model(X_train.shape[1], num_classes).to(self.device)

        # 6. 设置优化器和损失函数
        optimizer = optim.AdamW(self.model.parameters(), lr=0.001, weight_decay=1e-4)
        criterion = nn.CrossEntropyLoss(weight=class_weights_tensor)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=10, factor=0.5)

        # 7. 训练循环
        train_losses, val_losses = [], []
        train_accs, val_accs = [], []
        best_val_acc = 0
        patience_counter = 0
        patience = 20

        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0
            train_correct = 0
            train_total = 0

            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)

                optimizer.zero_grad()
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()

                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                optimizer.step()

                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += batch_y.size(0)
                train_correct += (predicted == batch_y).sum().item()

            # 验证阶段
            self.model.eval()
            val_loss = 0
            val_correct = 0
            val_total = 0

            with torch.no_grad():
                for batch_x, batch_y in val_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = self.model(batch_x)
                    loss = criterion(outputs, batch_y)

                    val_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += batch_y.size(0)
                    val_correct += (predicted == batch_y).sum().item()

            # 计算准确率
            train_acc = 100. * train_correct / train_total
            val_acc = 100. * val_correct / val_total
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(val_loader)

            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)
            train_accs.append(train_acc)
            val_accs.append(val_acc)

            # 学习率调度
            scheduler.step(avg_val_loss)

            # 早停检查
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                torch.save(self.model.state_dict(), 'best_improved_model.pth')
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f'早停触发，在第 {epoch+1} 轮停止训练')
                break

            if (epoch + 1) % 10 == 0:
                print(f'Epoch [{epoch+1}/{epochs}]')
                print(f'Train Loss: {avg_train_loss:.4f}, Train Acc: {train_acc:.2f}%')
                print(f'Val Loss: {avg_val_loss:.4f}, Val Acc: {val_acc:.2f}%')
                print(f'Best Val Acc: {best_val_acc:.2f}%')
                print('-' * 50)

        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_improved_model.pth'))

        # 8. 最终测试
        test_accuracy = self.evaluate_improved_model(test_loader, y_test, fault_types)

        print(f'训练完成！最佳验证准确率: {best_val_acc:.2f}%')
        print(f'最终测试准确率: {test_accuracy:.2f}%')

        return {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'train_accs': train_accs,
            'val_accs': val_accs,
            'best_val_acc': best_val_acc,
            'test_accuracy': test_accuracy
        }

    def evaluate_improved_model(self, test_loader, y_test, fault_types):
        """评估改进的模型"""
        self.model.eval()
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for batch_x, batch_y in test_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                outputs = self.model(batch_x)
                _, predicted = torch.max(outputs, 1)

                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(batch_y.cpu().numpy())

        # 计算准确率
        accuracy = accuracy_score(all_targets, all_predictions) * 100

        # 绘制混淆矩阵
        cm = confusion_matrix(all_targets, all_predictions)

        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=[f'类别{i}' for i in range(len(cm))],
                   yticklabels=[f'类别{i}' for i in range(len(cm))])
        plt.title(f'改进模型混淆矩阵 (准确率: {accuracy:.2f}%)')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.tight_layout()
        plt.show()

        # 打印分类报告
        print("\n改进模型分类报告:")
        print(classification_report(all_targets, all_predictions))

        return accuracy

    def plot_improvement_comparison(self, original_accuracy, improved_results):
        """绘制改进效果对比"""
        plt.figure(figsize=(15, 10))

        # 准确率对比
        plt.subplot(2, 3, 1)
        systems = ['原始系统', '改进系统']
        accuracies = [original_accuracy, improved_results['test_accuracy']]
        colors = ['lightcoral', 'lightgreen']

        bars = plt.bar(systems, accuracies, color=colors, alpha=0.7)
        plt.title('系统准确率对比')
        plt.ylabel('准确率 (%)')
        plt.ylim(0, 100)

        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')

        # 添加目标线
        plt.axhline(y=95, color='red', linestyle='--', alpha=0.7, label='目标 (95%)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 训练历史
        plt.subplot(2, 3, 2)
        plt.plot(improved_results['train_accs'], 'b-', label='训练准确率', linewidth=2)
        plt.plot(improved_results['val_accs'], 'r-', label='验证准确率', linewidth=2)
        plt.title('训练历史')
        plt.xlabel('Epoch')
        plt.ylabel('准确率 (%)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 损失曲线
        plt.subplot(2, 3, 3)
        plt.plot(improved_results['train_losses'], 'b-', label='训练损失', linewidth=2)
        plt.plot(improved_results['val_losses'], 'r-', label='验证损失', linewidth=2)
        plt.title('损失曲线')
        plt.xlabel('Epoch')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 改进效果
        plt.subplot(2, 3, 4)
        improvement = improved_results['test_accuracy'] - original_accuracy
        improvement_ratio = (improvement / original_accuracy) * 100 if original_accuracy > 0 else 0

        categories = ['绝对提升\n(%)', '相对提升\n(%)']
        improvements = [improvement, improvement_ratio]
        colors = ['skyblue', 'orange']

        bars = plt.bar(categories, improvements, color=colors, alpha=0.7)
        plt.title('性能提升效果')
        plt.ylabel('提升幅度')

        for bar, imp in zip(bars, improvements):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{imp:+.1f}', ha='center', va='bottom', fontweight='bold')

        plt.grid(True, alpha=0.3)

        # 技术改进
        plt.subplot(2, 3, 5)
        plt.text(0.05, 0.95, '技术改进:', fontsize=12, fontweight='bold', transform=plt.gca().transAxes)
        improvements_text = [
            '✓ 高级特征工程',
            '✓ 鲁棒数据预处理',
            '✓ 智能特征选择',
            '✓ 高级数据增强',
            '✓ 改进模型架构',
            '✓ 类别权重平衡',
            '✓ 自适应学习率',
            '✓ 早停和正则化'
        ]

        for i, text in enumerate(improvements_text):
            plt.text(0.05, 0.85 - i*0.09, text, fontsize=9, transform=plt.gca().transAxes)

        plt.axis('off')

        # 结果总结
        plt.subplot(2, 3, 6)
        plt.text(0.05, 0.95, '结果总结:', fontsize=12, fontweight='bold', transform=plt.gca().transAxes)

        target_reached = improved_results['test_accuracy'] >= 95
        summary_text = [
            f'原始准确率: {original_accuracy:.1f}%',
            f'改进准确率: {improved_results["test_accuracy"]:.1f}%',
            f'绝对提升: {improvement:+.1f}%',
            f'相对提升: {improvement_ratio:+.1f}%',
            '',
            f'目标达成: {"✅ 是" if target_reached else "❌ 否"}',
            '',
            '🎉 显著改善!' if improvement > 20 else '📈 有所改善' if improvement > 5 else '⚠️ 改善有限'
        ]

        for i, text in enumerate(summary_text):
            color = 'green' if '✅' in text or '🎉' in text else 'orange' if '📈' in text else 'red' if '❌' in text or '⚠️' in text else 'black'
            plt.text(0.05, 0.85 - i*0.08, text, fontsize=9,
                    transform=plt.gca().transAxes, color=color)

        plt.axis('off')

        plt.tight_layout()
        plt.savefig('improvement_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

def test_improved_system():
    """测试改进的故障诊断系统"""
    print("=" * 80)
    print("改进的六相永磁同步电机故障诊断系统测试")
    print("目标: 将准确率从29%提升到95%以上")
    print("=" * 80)

    # 1. 初始化系统
    print("\n1. 初始化改进系统")
    print("-" * 50)

    improved_system = ImprovedFaultDiagnosis()
    original_system = FaultDiagnosisSystem()

    # 2. 生成测试数据
    print("\n2. 生成测试数据")
    print("-" * 50)

    X, y, fault_types = original_system.generate_sample_data(
        num_classes=13,
        samples_per_class=100,  # 增加样本数量
        signal_length=1024,
        use_external=False
    )

    print(f"生成数据形状: {X.shape}")
    print(f"故障类型: {fault_types}")

    # 3. 训练原始系统作为基线
    print("\n3. 训练原始系统 (基线)")
    print("-" * 50)

    try:
        # 快速训练原始系统
        X_train_orig, X_test_orig, y_train_orig, y_test_orig = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        X_train_orig, X_val_orig, y_train_orig, y_val_orig = train_test_split(
            X_train_orig, y_train_orig, test_size=0.2, random_state=42, stratify=y_train_orig
        )

        original_history = original_system.train_model(
            X_train_orig, y_train_orig, X_val_orig, y_val_orig,
            epochs=20, batch_size=16
        )

        original_results = original_system.evaluate_model(X_test_orig, y_test_orig, fault_types)
        original_accuracy = original_results['accuracy']

        print(f"原始系统准确率: {original_accuracy:.2f}%")

    except Exception as e:
        print(f"原始系统训练失败: {e}")
        original_accuracy = 29.17  # 使用已知的基线准确率
        print(f"使用已知基线准确率: {original_accuracy:.2f}%")

    # 4. 训练改进系统
    print("\n4. 训练改进系统")
    print("-" * 50)

    try:
        improved_results = improved_system.train_improved_model(
            X, y, fault_types,
            test_size=0.2,
            epochs=80,
            batch_size=32
        )

        print(f"改进系统准确率: {improved_results['test_accuracy']:.2f}%")

        # 5. 绘制对比结果
        print("\n5. 绘制改进效果对比")
        print("-" * 50)

        improved_system.plot_improvement_comparison(original_accuracy, improved_results)

        # 6. 结果总结
        print("\n6. 结果总结")
        print("-" * 50)

        improvement = improved_results['test_accuracy'] - original_accuracy
        improvement_ratio = (improvement / original_accuracy) * 100 if original_accuracy > 0 else 0
        target_reached = improved_results['test_accuracy'] >= 95

        print(f"✅ 原始准确率: {original_accuracy:.2f}%")
        print(f"✅ 改进准确率: {improved_results['test_accuracy']:.2f}%")
        print(f"✅ 绝对提升: {improvement:+.2f}%")
        print(f"✅ 相对提升: {improvement_ratio:+.1f}%")
        print(f"✅ 目标达成: {'是' if target_reached else '否'}")

        if target_reached:
            print("\n🎉 恭喜！成功达到95%的目标准确率！")
        elif improved_results['test_accuracy'] >= 80:
            print("\n👍 接近目标！准确率已显著提升")
        elif improvement > 10:
            print("\n📈 显著改善！系统性能大幅提升")
        else:
            print("\n⚠️ 仍需进一步优化")

        return True

    except Exception as e:
        print(f"改进系统训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_improved_system()
