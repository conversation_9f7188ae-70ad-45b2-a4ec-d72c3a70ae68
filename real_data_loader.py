"""
真实六相永磁同步电机故障数据加载器
专门用于加载和预处理真实的故障电流数据
"""

import numpy as np
import os
import scipy.io as sio
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class RealFaultDataLoader:
    """真实故障数据加载器"""
    
    def __init__(self, data_root='故障电流与全部电流'):
        self.data_root = data_root
        self.fault_types = []
        self.data_cache = {}
        
    def load_all_fault_data(self, max_samples_per_class=1000, signal_length=1024):
        """加载所有真实故障数据"""
        print("=" * 60)
        print("加载真实六相永磁同步电机故障数据")
        print("=" * 60)
        
        X_all = []
        y_all = []
        fault_types = []
        
        # 1. 加载正常状态数据
        print("\n1. 加载正常状态数据")
        normal_data = self._load_normal_data(max_samples_per_class, signal_length)
        if normal_data is not None:
            X_all.extend(normal_data)
            y_all.extend([0] * len(normal_data))
            fault_types.append('正常状态')
            print(f"   正常状态: {len(normal_data)} 个样本")
        
        # 2. 加载故障数据
        print("\n2. 加载故障数据")
        fault_data_dir = os.path.join(self.data_root, '故障电流')

        if os.path.exists(fault_data_dir):
            fault_folders = [f for f in os.listdir(fault_data_dir)
                           if os.path.isdir(os.path.join(fault_data_dir, f))]
            fault_folders.sort()

            class_idx = 1  # 从1开始，0是正常状态

            for folder in fault_folders:
                folder_path = os.path.join(fault_data_dir, folder)
                fault_samples = self._load_fault_folder(folder_path, max_samples_per_class, signal_length)

                if fault_samples is not None and len(fault_samples) > 0:
                    X_all.extend(fault_samples)
                    y_all.extend([class_idx] * len(fault_samples))
                    fault_types.append(folder)
                    print(f"   {folder}: {len(fault_samples)} 个样本")
                    class_idx += 1
        else:
            print(f"   故障数据目录不存在: {fault_data_dir}")
            print("   将生成模拟故障数据...")

            # 生成模拟故障数据
            simulated_fault_types = [
                'A相增益故障1.5', 'A相增益故障0', 'A相漂移故障5', 'A相漂移故障0',
                'S1duan', 'S2duan', 'S3duan', 'S4duan', 'S5duan', 'S6duan',
                'S1andS4duan', 'S2andS5duan'
            ]

            class_idx = 1
            for fault_type in simulated_fault_types:
                simulated_samples = self._generate_simulated_fault_data(
                    fault_type, max_samples_per_class, signal_length
                )
                X_all.extend(simulated_samples)
                y_all.extend([class_idx] * len(simulated_samples))
                fault_types.append(fault_type)
                print(f"   {fault_type} (模拟): {len(simulated_samples)} 个样本")
                class_idx += 1
        
        # 3. 转换为numpy数组
        if len(X_all) > 0:
            X_all = np.array(X_all)
            y_all = np.array(y_all)
            
            print(f"\n数据加载完成:")
            print(f"  总样本数: {len(X_all)}")
            print(f"  信号长度: {X_all.shape[1] if len(X_all.shape) > 1 else 'N/A'}")
            print(f"  故障类型数: {len(fault_types)}")
            
            # 数据质量检查
            self._check_data_quality(X_all, y_all, fault_types)
            
            return X_all, y_all, fault_types
        else:
            print("❌ 未找到有效的故障数据")
            return None, None, None
    
    def _load_normal_data(self, max_samples, signal_length):
        """加载正常状态数据"""
        normal_data = []
        
        # 尝试从不同位置加载正常数据
        normal_paths = [
            os.path.join(self.data_root, '正常'),
            os.path.join(self.data_root, '全部电流', '正常'),
            '正常'
        ]
        
        for normal_path in normal_paths:
            if os.path.exists(normal_path):
                print(f"   从 {normal_path} 加载正常数据")
                
                # 查找.mat文件
                mat_files = [f for f in os.listdir(normal_path) if f.endswith('.mat')]
                
                for mat_file in mat_files:
                    file_path = os.path.join(normal_path, mat_file)
                    try:
                        mat_data = sio.loadmat(file_path)
                        
                        # 查找电流数据
                        for key in mat_data.keys():
                            if not key.startswith('__') and isinstance(mat_data[key], np.ndarray):
                                data = mat_data[key]
                                
                                if len(data.shape) == 2 and data.shape[1] == 6:  # 六相电流
                                    # 提取信号段
                                    samples = self._extract_signal_segments(data, signal_length, max_samples)
                                    normal_data.extend(samples)
                                    print(f"     从 {mat_file} 提取 {len(samples)} 个样本")
                                    
                                    if len(normal_data) >= max_samples:
                                        break
                        
                        if len(normal_data) >= max_samples:
                            break
                            
                    except Exception as e:
                        print(f"     加载 {mat_file} 失败: {e}")
                        continue
                
                if len(normal_data) > 0:
                    break
        
        return normal_data[:max_samples] if normal_data else None
    
    def _load_fault_folder(self, folder_path, max_samples, signal_length):
        """加载单个故障类型文件夹的数据"""
        fault_data = []
        
        if not os.path.exists(folder_path):
            return None
        
        # 查找.mat文件
        mat_files = [f for f in os.listdir(folder_path) if f.endswith('.mat')]
        
        for mat_file in mat_files:
            file_path = os.path.join(folder_path, mat_file)
            try:
                mat_data = sio.loadmat(file_path)
                
                # 查找电流数据 (通常是变量'i')
                if 'i' in mat_data:
                    data = mat_data['i']
                else:
                    # 查找其他可能的电流数据变量
                    for key in mat_data.keys():
                        if not key.startswith('__') and isinstance(mat_data[key], np.ndarray):
                            data = mat_data[key]
                            break
                    else:
                        continue
                
                if len(data.shape) == 2 and data.shape[1] == 6:  # 六相电流
                    # 提取信号段
                    samples = self._extract_signal_segments(data, signal_length, max_samples - len(fault_data))
                    fault_data.extend(samples)
                    
                    if len(fault_data) >= max_samples:
                        break
                        
            except Exception as e:
                print(f"     加载 {mat_file} 失败: {e}")
                continue
        
        return fault_data[:max_samples] if fault_data else None
    
    def _extract_signal_segments(self, data, signal_length, max_segments):
        """从长信号中提取固定长度的信号段"""
        segments = []
        
        if data.shape[0] < signal_length:
            # 如果数据太短，进行零填充
            padded_data = np.zeros((signal_length, data.shape[1]))
            padded_data[:data.shape[0], :] = data
            # 只取第一相作为代表
            segments.append(padded_data[:, 0])
        else:
            # 滑动窗口提取多个段
            step_size = max(1, (data.shape[0] - signal_length) // max(1, max_segments - 1))
            
            for start_idx in range(0, data.shape[0] - signal_length + 1, step_size):
                if len(segments) >= max_segments:
                    break
                
                segment = data[start_idx:start_idx + signal_length, 0]  # 只取第一相
                
                # 检查信号质量
                if np.std(segment) > 1e-6:  # 避免常数信号
                    segments.append(segment)
        
        return segments

    def _generate_simulated_fault_data(self, fault_type, num_samples, signal_length):
        """生成模拟故障数据"""
        samples = []

        for _ in range(num_samples):
            t = np.linspace(0, signal_length / 1000, signal_length)  # 假设1kHz采样率
            base_freq = 50  # 基础频率50Hz
            base_signal = np.sin(2 * np.pi * base_freq * t)

            if 'A相增益' in fault_type:
                # 增益故障
                gain = 1.5 if '1.5' in fault_type else 0.1
                signal = gain * base_signal + 0.1 * np.random.randn(signal_length)

            elif 'A相漂移' in fault_type:
                # 漂移故障
                drift = 5.0 if '5' in fault_type else 0.1
                signal = base_signal + drift + 0.1 * np.random.randn(signal_length)

            elif 'duan' in fault_type:
                # 开关管故障
                if 'and' in fault_type:
                    # 多开关管故障
                    fault_freq1 = np.random.uniform(80, 120)
                    fault_freq2 = np.random.uniform(150, 200)
                    signal = base_signal + 0.3 * np.sin(2 * np.pi * fault_freq1 * t)
                    signal += 0.3 * np.sin(2 * np.pi * fault_freq2 * t)
                else:
                    # 单开关管故障
                    fault_freq = np.random.uniform(100, 200)
                    signal = base_signal + 0.5 * np.sin(2 * np.pi * fault_freq * t)

                signal += 0.15 * np.random.randn(signal_length)

            else:
                # 默认故障
                fault_freq = np.random.uniform(100, 300)
                signal = base_signal + 0.4 * np.sin(2 * np.pi * fault_freq * t)
                signal += 0.1 * np.random.randn(signal_length)

            samples.append(signal)

        return samples

    def _check_data_quality(self, X, y, fault_types):
        """检查数据质量"""
        print(f"\n3. 数据质量检查")
        print("-" * 40)
        
        for class_idx in range(len(fault_types)):
            class_mask = y == class_idx
            class_data = X[class_mask]
            
            if len(class_data) > 0:
                # 计算统计信息
                mean_val = np.mean(class_data)
                std_val = np.std(class_data)
                min_val = np.min(class_data)
                max_val = np.max(class_data)
                
                # 检查数据质量
                quality_issues = []
                if std_val < 1e-6:
                    quality_issues.append("常数信号")
                if np.any(np.isnan(class_data)):
                    quality_issues.append("包含NaN")
                if np.any(np.isinf(class_data)):
                    quality_issues.append("包含Inf")
                
                status = "异常: " + ", ".join(quality_issues) if quality_issues else "正常"
                
                print(f"  {fault_types[class_idx]:20s}: {len(class_data):4d} 样本")
                print(f"    均值={mean_val:8.4f}, 标准差={std_val:8.4f}")
                print(f"    范围=[{min_val:8.4f}, {max_val:8.4f}], 状态={status}")
    
    def preprocess_data(self, X, y, normalize=True, remove_outliers=True):
        """预处理数据"""
        print(f"\n4. 数据预处理")
        print("-" * 40)
        
        X_processed = X.copy()
        y_processed = y.copy()
        
        # 1. 移除异常值
        if remove_outliers:
            print("  移除异常值...")
            valid_mask = np.ones(len(X_processed), dtype=bool)
            
            for i, signal in enumerate(X_processed):
                # 检查是否为常数信号
                if np.std(signal) < 1e-6:
                    valid_mask[i] = False
                    continue
                
                # 检查是否包含异常值
                if np.any(np.isnan(signal)) or np.any(np.isinf(signal)):
                    valid_mask[i] = False
                    continue
                
                # 检查是否超出合理范围
                if np.abs(np.mean(signal)) > 1000 or np.std(signal) > 1000:
                    valid_mask[i] = False
                    continue
            
            removed_count = np.sum(~valid_mask)
            X_processed = X_processed[valid_mask]
            y_processed = y_processed[valid_mask]
            
            print(f"    移除了 {removed_count} 个异常样本")
            print(f"    剩余 {len(X_processed)} 个有效样本")
        
        # 2. 数据标准化
        if normalize:
            print("  数据标准化...")
            scaler = StandardScaler()
            
            # 对每个信号独立标准化
            for i in range(len(X_processed)):
                signal = X_processed[i].reshape(-1, 1)
                X_processed[i] = scaler.fit_transform(signal).flatten()
            
            print("    完成信号标准化")
        
        print(f"  预处理完成: {X_processed.shape}")
        
        return X_processed, y_processed
    
    def visualize_data_distribution(self, X, y, fault_types, save_path='data_distribution.png'):
        """可视化数据分布"""
        print(f"\n5. 数据分布可视化")
        print("-" * 40)
        
        plt.figure(figsize=(15, 10))
        
        # 1. 各类别样本数量
        plt.subplot(2, 3, 1)
        unique_labels, counts = np.unique(y, return_counts=True)
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
        
        bars = plt.bar(range(len(unique_labels)), counts, color=colors)
        plt.title('各故障类型样本数量')
        plt.xlabel('故障类型')
        plt.ylabel('样本数量')
        plt.xticks(range(len(unique_labels)), 
                  [fault_types[i] if i < len(fault_types) else f'类别{i}' 
                   for i in unique_labels], rotation=45)
        
        # 添加数值标签
        for bar, count in zip(bars, counts):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    str(count), ha='center', va='bottom')
        
        # 2. 信号示例
        plt.subplot(2, 3, 2)
        for class_idx in range(min(5, len(fault_types))):
            class_mask = y == class_idx
            if np.any(class_mask):
                sample_signal = X[class_mask][0]
                plt.plot(sample_signal[:200], label=fault_types[class_idx], alpha=0.7)
        
        plt.title('各类型信号示例 (前200点)')
        plt.xlabel('时间点')
        plt.ylabel('幅值')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 3. 信号统计特征
        plt.subplot(2, 3, 3)
        means = []
        stds = []
        
        for class_idx in range(len(fault_types)):
            class_mask = y == class_idx
            if np.any(class_mask):
                class_data = X[class_mask]
                means.append(np.mean(class_data))
                stds.append(np.std(class_data))
            else:
                means.append(0)
                stds.append(0)
        
        x_pos = range(len(fault_types))
        plt.bar(x_pos, means, alpha=0.7, label='均值')
        plt.bar(x_pos, stds, alpha=0.7, label='标准差')
        plt.title('各类型统计特征')
        plt.xlabel('故障类型')
        plt.ylabel('数值')
        plt.xticks(x_pos, fault_types, rotation=45)
        plt.legend()
        
        # 4. 数据质量评估
        plt.subplot(2, 3, 4)
        quality_scores = []
        
        for class_idx in range(len(fault_types)):
            class_mask = y == class_idx
            if np.any(class_mask):
                class_data = X[class_mask]
                # 简单的质量评分：基于标准差和样本数
                std_score = min(1.0, np.std(class_data) / 10.0)
                count_score = min(1.0, len(class_data) / 100.0)
                quality_score = (std_score + count_score) / 2
                quality_scores.append(quality_score)
            else:
                quality_scores.append(0)
        
        bars = plt.bar(range(len(fault_types)), quality_scores, 
                      color=['green' if s > 0.7 else 'orange' if s > 0.4 else 'red' 
                            for s in quality_scores])
        plt.title('数据质量评估')
        plt.xlabel('故障类型')
        plt.ylabel('质量评分')
        plt.xticks(range(len(fault_types)), fault_types, rotation=45)
        plt.ylim(0, 1)
        
        # 添加质量标准线
        plt.axhline(y=0.7, color='green', linestyle='--', alpha=0.5, label='优秀')
        plt.axhline(y=0.4, color='orange', linestyle='--', alpha=0.5, label='良好')
        plt.legend()
        
        # 5. 数据摘要
        plt.subplot(2, 3, 5)
        plt.text(0.1, 0.9, '数据摘要:', fontsize=14, fontweight='bold', transform=plt.gca().transAxes)
        
        summary_text = [
            f'总样本数: {len(X)}',
            f'故障类型数: {len(fault_types)}',
            f'信号长度: {X.shape[1] if len(X.shape) > 1 else "N/A"}',
            f'数据维度: {X.shape}',
            '',
            '数据质量:',
            f'  优秀类型: {sum(1 for s in quality_scores if s > 0.7)}',
            f'  良好类型: {sum(1 for s in quality_scores if 0.4 < s <= 0.7)}',
            f'  需改进类型: {sum(1 for s in quality_scores if s <= 0.4)}'
        ]
        
        for i, text in enumerate(summary_text):
            plt.text(0.1, 0.8 - i*0.08, text, fontsize=10, transform=plt.gca().transAxes)
        
        plt.axis('off')
        
        # 6. 建议
        plt.subplot(2, 3, 6)
        plt.text(0.1, 0.9, '数据优化建议:', fontsize=14, fontweight='bold', transform=plt.gca().transAxes)
        
        avg_quality = np.mean(quality_scores)
        min_samples = min(counts)
        max_samples = max(counts)
        
        suggestions = []
        if avg_quality < 0.6:
            suggestions.append('• 提高数据采集质量')
        if max_samples / min_samples > 3:
            suggestions.append('• 平衡各类别样本数量')
        if min_samples < 50:
            suggestions.append('• 增加样本数量较少的类别')
        if len(suggestions) == 0:
            suggestions.append('• 数据质量良好，可直接使用')
            suggestions.append('• 建议进行数据增强以提升性能')
        
        for i, suggestion in enumerate(suggestions):
            plt.text(0.1, 0.8 - i*0.1, suggestion, fontsize=10, transform=plt.gca().transAxes)
        
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"  数据分布图已保存为: {save_path}")

def test_real_data_loader():
    """测试真实数据加载器"""
    print("测试真实故障数据加载器...")
    
    loader = RealFaultDataLoader()
    
    # 加载数据
    X, y, fault_types = loader.load_all_fault_data(
        max_samples_per_class=100,
        signal_length=1024
    )
    
    if X is not None:
        # 预处理数据
        X_processed, y_processed = loader.preprocess_data(X, y)
        
        # 可视化数据分布
        loader.visualize_data_distribution(X_processed, y_processed, fault_types)
        
        return X_processed, y_processed, fault_types
    else:
        print("❌ 数据加载失败")
        return None, None, None

if __name__ == "__main__":
    test_real_data_loader()
