clear;clc;close all
% 生成时间向量，步长调整为使数据点数量为500
t = 0: 1 / 49: 10; 
% 生成正弦信号x1
x1 = sin(2 * pi * t); 
% 生成正弦信号x2
x2 = sin(2 * pi * t + pi / 2); 
% 绘制x1和x2的波形
figure
plot(t, x1, 'r', t, x2, 'b'); 
legend('x1', 'x2');
xlabel('时间');
ylabel('幅值');
title('不同相位的正弦信号（500个数据点）');

% 设置采样率
fs = 1;
% 初始化互相关函数结果向量
Rij = zeros(1, 2*length(x1) - 1);
for t_shift = -length(x1) + 1:length(x1) - 1
    if t_shift >= 0
        % 当时间偏移t_shift大于等于0时的计算公式
        Rij(t_shift + length(x1)) = sum(x1(t_shift*fs + 1:length(x1)).*x2(1:length(x1) - t_shift*fs));
    else
        % 当时间偏移t_shift小于0时的计算公式
        Rij(t_shift + length(x1)) = sum(x1(1:length(x1) + t_shift*fs).*x2(-t_shift*fs + 1:length(x1)));
    end
end
% 生成时间偏移点向量
t_shift_points = (-length(x1) + 1:length(x1) - 1)*fs;
% 绘制互相关函数图像
plot(t_shift_points, Rij);
xlabel('Time - shift points');
ylabel('Cross - correlation coefficient');
title('Cross - correlation of two sinusoidal signals');
% 找到互相关函数最大值及其对应的时间偏移点
[max_corr, max_index] = max(Rij);
max_t_shift = (max_index - length(x1))*fs;
% 在图像上标注最大值点
hold on;
plot(max_t_shift, max_corr, 'ro');
text(max_t_shift, max_corr, ['Max point: X=', num2str(max_t_shift)], 'Color', 'green');
hold off;


%%
fs = 1000; % 采样率
x1=x1';
x2=x2';
x2_calibrated = currentSignalCalibration(x1, x2, fs);
figure
% 绘制原始信号
subplot(2,1,1);
plot(t, x1);
hold on;
plot(t, x2);
legend('x1', 'x2');
title('Original Signals');
xlabel('Time');
ylabel('Amplitude');
hold off;

% 绘制校准后信号
subplot(2,1,2);
plot(t, x1);
hold on;
plot(t, x2_calibrated);
legend('x1', 'x2_calibrated');
title('Calibrated Signals');
xlabel('Time');
ylabel('Amplitude');


%%
fs = 1; % 采样率
% 计算原始信号的互相关函数
Rij_original = zeros(1, 2*length(x1) - 1);
for t_shift = -length(x1) + 1:length(x1) - 1
    if t_shift >= 0
        Rij_original(t_shift + length(x1)) = sum(x1(t_shift*fs + 1:length(x1)).*x2(1:length(x1) - t_shift*fs));
    else
        Rij_original(t_shift + length(x1)) = sum(x1(1:length(x1) + t_shift*fs).*x2(-t_shift*fs + 1:length(x1)));
    end
end

% 计算校准后信号的互相关函数
Rij_calibrated = zeros(1, 2*length(x1) - 1);
for t_shift = -length(x1) + 1:length(x1) - 1
    if t_shift >= 0
        Rij_calibrated(t_shift + length(x1)) = sum(x1(t_shift*fs + 1:length(x1)).*x2_calibrated(1:length(x1) - t_shift*fs));
    else
        Rij_calibrated(t_shift + length(x1)) = sum(x1(1:length(x1) + t_shift*fs).*x2_calibrated(-t_shift*fs + 1:length(x1)));
    end
end

[max_corr_original, max_index_original] = max(Rij_original);
max_t_shift_original = (max_index_original - length(x1)) * fs;
[max_corr_calibrated, max_index_calibrated] = max(Rij_calibrated);
max_t_shift_calibrated = (max_index_calibrated - length(x1)) * fs;
figure
% 绘制原始信号的互相关函数
subplot(2, 1, 1);
t_shift_points = (-length(x1) + 1:length(x1) - 1) * fs;
plot(t_shift_points, Rij_original);
hold on;
plot(max_t_shift_original, max_corr_original, 'ro', 'MarkerSize', 8); % 绘制最大值点（红色圆圈）
plot([max_t_shift_original, max_t_shift_original], [min(Rij_original), max_corr_original], 'k--', 'LineWidth', 1.5); % 绘制最大值点处垂直线（黑色虚线）
text(max_t_shift_original, max_corr_original, ['Max point: X=', num2str(max_t_shift_original)], 'Color', 'green');
title('Cross - correlation of Original Signals');
xlabel('Time - shift points');
ylabel('Cross - correlation coefficient');
hold off;
% 绘制校准后信号的互相关函数
subplot(2, 1, 2);
plot(t_shift_points, Rij_calibrated);
hold on;
plot(max_t_shift_calibrated, max_corr_calibrated, 'ro', 'MarkerSize', 8); % 绘制最大值点（红色圆圈）
plot([max_t_shift_calibrated, max_t_shift_calibrated], [min(Rij_calibrated), max_corr_calibrated], 'k--', 'LineWidth', 1.5); % 绘制最大值点处垂直线（黑色虚线）
text(max_t_shift_calibrated, max_corr_calibrated, ['Max point: X=', num2str(max_t_shift_calibrated)], 'Color', 'green');
title('Cross - correlation of Calibrated Signals');
xlabel('Time - shift points');
ylabel('Cross - correlation coefficient');
hold off;



%% 公式 6 定义种子电流信号（这里假设一种简单的示例情况，你可根据实际需求调整）
% clear;clc;close all
% 设置公共频率和幅值，你可按需修改
frequency = 5; % 频率，单位Hz
amplitude = 2; % 幅值，单位可根据实际情况设定，比如安培
% 生成时间向量，设定合适的范围和步长，这里时间范围是0到5秒，步长为0.01秒
t = 0:0.01:5; 
% 初始化数据集矩阵，每一列代表一个样本信号
data_set = zeros(length(t), 10); 
% 波动幅度相关参数，可调整以改变波动大小，这里设定波动幅度在 [-0.5, 0.5] 之间
fluctuation_amplitude = 0.5; 
% 生成10个不同的带有波动的正弦电流信号样本（频率、幅值相同）
for i = 1:10
    % 随机生成相位，范围在0到2*pi之间
    phase = rand * 2 * pi; 

    % 生成带有波动的正弦信号
    for j = 1:length(t)
        % 生成波动项，这里简单使用随机噪声模拟波动，你也可以使用更复杂的波动模型
        fluctuation = (rand - 0.5) * fluctuation_amplitude; 
        data_set(j, i) = (amplitude + fluctuation) * sin(2 * pi * frequency * t(j) + phase);
    end
end

%展示图像（之前二维子图展示部分，此处省略详细注释，可参考之前代码理解）
figure('Position', [100, 100, 800, 600]); 
for i = 1:10
    subplot(5, 2, i); 
    plot(t, data_set(1:length(t), i), 'r'); 
    title(['Sample ', num2str(i)]); 
    xlabel('Time (s)'); 
    ylabel('Amplitude (A)'); 
end
sgtitle('10 Samples of Fluctuating Sinusoidal Current Signals with the Same Frequency and Amplitude');

plot(data_set(1:500,1), 'r');


% 以下是校准信号部分
fs = 1; % 采样率，你可根据实际数据集采样情况修改
calibrated_signals = []; % 初始化校准后信号矩阵
for i = 1:9
    % 假设数据集第一列是参考信号，后面列是待校准信号（根据实际情况调整索引）
    reference_signal = data_set(:, 1); 
    signal_to_calibrate = data_set(:, i + 1);
    calibrated_signal = currentSignalCalibration(reference_signal, signal_to_calibrate, fs);
  temp_signals = []; % 临时存储去除0值后的校准信号
   for row = 1:size(calibrated_signal, 1) % 遍历每一行
      if any(calibrated_signal(row, :) ~= 0) % 判断该行是否有非零元素
        temp_signals = [temp_signals; calibrated_signal(row, :)]; % 将非零行存入临时矩阵
      end
   end
calibrated_signal = temp_signals; % 更新校准信号矩阵
calibrated_signals = [calibrated_signals calibrated_signal]; % 将校准后的信号依次存入矩阵
end
calibrated_signals = [reference_signal calibrated_signals];
% 转置校准后信号矩阵，使其维度更符合后续三维绘图的布局（样本×时间）
calibrated_signals = calibrated_signals';

% 获取校准后信号的样本数量和时间点数
[num_samples, num_time_points] = size(calibrated_signals);
% 生成样本序号向量（用于三维图的一个维度）
sample_indices = 1:num_samples;
% 构建三维网格数据（用于三维绘图）
[X, Y] = meshgrid(sample_indices, t);
% 绘制三维视图展示校准后的数据图
% 绘制三维线性图展示校准后的数据图，将数据逐点绘制为线条形式
calibrated_signals=calibrated_signals';
figure;
hold on; % 保持图形状态，便于多次绘制线条
for i = 1:num_samples
    plot3(X(:,i), Y(:,i), calibrated_signals(:,i), 'LineWidth', 1.5); % 绘制每条样本信号对应的三维线条，可调整线宽等属性
end
hold off; % 关闭保持状态
title('3D Visualization of Calibrated Signals');
xlabel('Sample Index');
ylabel('Time (s)');
zlabel('Amplitude (A)');
view([-37.5, 30]); % 设置一个合适的视角，可根据喜好调整

seed_signals = calibrated_signals; % 直接使用校准后的信号作为种子电流信号，可按需调整

%% 公式 7 定义基线信号（基于种子电流信号）
q = 3; % 假设正常模式样本数量为3，可根据实际情况修改
iBs = mean(seed_signals(:, 1:q), 2); % 按列求平均得到基线信号（返回一个列向量，长度与信号长度一致）
figure;
t = 0:fs:(size(seed_signals, 1) - 1) * fs; % 根据种子电流信号长度生成时间向量
plot(t, sum(seed_signals, 2)); % 绘制叠加后的种子电流信号
title('Seed Current Signals');
xlabel('Time');
ylabel('Amplitude');
hold on;
plot(t, iBs, 'r', 'LineWidth', 2); % 绘制基线信号（红色，加粗线条以便区分）
legend('Seed Current Signals', 'Baseline Signal');
hold off;

figure;
t = 0:fs:(length(seed_signals(:, 1)) - 1) * fs; % 生成对应的时间向量
plot(t, sum(seed_signals, 2)); % 绘制叠加后的种子电流信号
title('Seed Current Signals');
xlabel('Time');
ylabel('Amplitude');
hold on;
plot(t, iBs, 'r', 'LineWidth', 2); % 绘制基线信号（红色，加粗线条以便区分）
legend('Seed Current Signals', 'Baseline Signal');
hold off;



%%
% 假设seed_signals是经过校准后的信号矩阵，维度为p*q（p为模式数量，q为每个模式的样本数量）
% 这里为了方便示例，假设seed_signals已经存在，并且其维度符合要求，实际应用中需根据真实数据情况进行调整

% 数据增强参数设置
nP = 2; % 比例影响因素扩展数量
nA = 2; % 附加影响因素扩展数量
ls = 0.001; % 系数变化步长
muBeta = 0; % 附加系数序列平均值
sigmaBeta = 0.01; % 附加系数序列标准差

% 调用数据增强函数
enhanced_seed_signals = dataEnhancement(seed_signals, nP, nA, ls, muBeta, sigmaBeta);
enhanced_signals=enhanced_seed_signals;
% 获取增强后信号的样本数量和时间点数
[num_time_points,num_samples] = size(enhanced_signals);
% 生成样本序号向量（用于三维图的一个维度）
sample_indices = 1:num_samples;
% 构建三维网格数据（用于三维绘图）
[X, Y] = meshgrid(sample_indices, t);
figure;
hold on; % 保持图形状态，便于多次绘制线条
for i = 1:num_samples
    plot3(X(:,i), Y(:,i), enhanced_signals(:,i), 'LineWidth', 1.5); % 绘制每条样本信号对应的三维线条，可调整线宽等属性
end
hold off; % 关闭保持状态title('3D Visualization of Enhanced Signals');
xlabel('Sample Index');
ylabel('Time (s)');
zlabel('Amplitude (A)');
view([-37.5, 30]); % 设置一个合适的视角，可根据喜好调整



% 假设enhanced_seed_signals是经过数据增强后的信号矩阵，iBs是基线信号（这里假设它们已经存在并且维度符合要求）
%%考虑所有电流信号具有同一相位和频率，
% 故障信息反映在扩展电流信号与基线电流信号的差中，
% 此差定义为CRS，CRS的幅度主要由退化程度决定，CRS的特征由信号模式决定。
enhanced_seed_signals=enhanced_seed_signals;
% 获取增强信号的维度信息
[p, q_extended] = size(enhanced_seed_signals);
% 计算训练样本的CRS
CRS_Training = zeros(p, q_extended);
for i = 1:p
    for j = 1:q_extended
        CRS_Training(i, j) = enhanced_seed_signals(i, j) - iBs(i, 1); % 根据公式(13)计算CRS
    end
end