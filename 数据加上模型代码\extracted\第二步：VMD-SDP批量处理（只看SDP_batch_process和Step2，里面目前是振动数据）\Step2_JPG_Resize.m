
%重新调整图像大小，否则图像过大，不利于训练！
clc
clear all
load data_total_6lei.mat
% load Working_condition0.mat
% 给数据加标签
bv = 120;    %每种状态数据有120组
% 加标签值
hhh = size(data,2);
for i=1:size(data,1)/bv
    label(1+bv*(i-1):bv*i,1)=i;
end

%% 工况1-SDP图压缩
inputFolder = '工况1-SDP'; 
% 获取文件夹中所有的图像文件
imageFiles = dir(fullfile(inputFolder, '*.jpg'));
% 提取文件名中的数字部分
imageNumbers = cellfun(@(x) sscanf(x, '%d.png'), {imageFiles.name});
% 按照数字部分排序
[sortedNumbers, sortOrder] = sort(imageNumbers);
% 根据排序后的索引处理图像文件
sortedImageFiles = imageFiles(sortOrder);
numImages = length(sortedImageFiles);
imageSize = [64,64,3]; % 目标图像尺寸
for i = 1:numImages
    i
    imageFileName = sortedImageFiles(i).name;
    imagePath = fullfile(inputFolder, imageFileName);
    % 读取图像
    img = imread(imagePath);
    % 调整图像尺寸
    imgResized = imresize(img, imageSize(1:2)); 
    imgg = double(imgResized)/255;
    Resize_Working_condition1{1,i} = imgg;
    Resize_Working_condition1{2,i} = label(i);
end
save Resize_Working_condition1.mat Resize_Working_condition1

%% 工况2-SDP图压缩
inputFolder = '工况2-SDP'; 
% 获取文件夹中所有的图像文件
imageFiles = dir(fullfile(inputFolder, '*.jpg'));
% 提取文件名中的数字部分
imageNumbers = cellfun(@(x) sscanf(x, '%d.png'), {imageFiles.name});
% 按照数字部分排序
[sortedNumbers, sortOrder] = sort(imageNumbers);
% 根据排序后的索引处理图像文件
sortedImageFiles = imageFiles(sortOrder);
numImages = length(sortedImageFiles);
imageSize = [64,64,3]; % 目标图像尺寸
for i = 1:numImages
    i
    imageFileName = sortedImageFiles(i).name;
    imagePath = fullfile(inputFolder, imageFileName);
    % 读取图像
    img = imread(imagePath);
    % 调整图像尺寸
    imgResized = imresize(img, imageSize(1:2)); 
    imgg = double(imgResized)/255;
    Resize_Working_condition2{1,i} = imgg;
    Resize_Working_condition2{2,i} = label(i);
end
save Resize_Working_condition2.mat Resize_Working_condition2

disp('处理完成！');
